<?php

namespace Database\Seeders;

use App\Models\Accommodation;
use App\Models\AccommodationGroup;
use App\Models\User;
use Illuminate\Database\Seeder;

class AccommodationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all users
        $users = User::all();

        if ($users->isEmpty()) {
            $this->command->info('No users found. Skipping accommodation seeding.');
            return;
        }

        // Get all groups
        $groups = AccommodationGroup::all();

        if ($groups->isEmpty()) {
            $this->command->info('No accommodation groups found. Run AccommodationGroupSeeder first.');
            return;
        }

        // Create accommodations and assign them to groups
        foreach ($users as $user) {
            // Get groups for this user
            $userGroups = $groups->where('user_id', $user->id);

            if ($userGroups->isEmpty()) {
                continue;
            }

            // Get the user's subscription
            $subscription = $user->planSubscription('main');

            // Default to 5 accommodations if no subscription or feature found
            $accommodationLimit = 5;

            if ($subscription) {
                // Find the accommodations feature
                $feature = $subscription->plan->features()
                    ->where('name', 'Accommodations')
                    ->first();

                if ($feature) {
                    $accommodationLimit = $feature->value;
                }
            }

            // Count how many accommodations we've created for this user
            $accommodationCount = 0;

            // Create 1 accommodation for each group, up to the limit
            foreach ($userGroups as $group) {
                if ($accommodationCount >= $accommodationLimit) {
                    break;
                }

                Accommodation::factory()
                    ->count(1)
                    ->create([
                        'user_id' => $user->id,
                        'team_id' => $user->currentTeam?->id,
                        'accommodation_group_id' => $group->id,
						'published' => false
                    ]);

                $accommodationCount++;
            }
        }
    }
}
