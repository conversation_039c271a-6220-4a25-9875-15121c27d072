<?php

namespace Database\Factories;

use App\Models\Blog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Blog>
 */
class BlogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Blog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $title = $this->faker->sentence(6, true);
        $content = $this->generateBlogContent();
        
        return [
            'user_id' => User::factory(),
            'title' => $title,
            'slug' => Str::slug($title),
            'subtitle' => $this->faker->sentence(8, true),
            'content' => $content,
            'meta_title' => $this->faker->boolean(60) ? $this->faker->sentence(4, true) : null, // 60% chance of custom meta title
            'meta_description' => $this->faker->boolean(60) ? $this->faker->sentence(12, true) : null, // 60% chance of custom meta description
            'feature_image' => null,
            'published' => $this->faker->boolean(80), // 80% chance of being published
            'post_date' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ];
    }

    /**
     * Indicate that the blog post is published.
     */
    public function published(): static
    {
        return $this->state(fn (array $attributes) => [
            'published' => true,
            'post_date' => $this->faker->dateTimeBetween('-6 months', 'now'),
        ]);
    }

    /**
     * Indicate that the blog post is a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'published' => false,
            'post_date' => null,
        ]);
    }

    /**
     * Generate realistic blog content with HTML formatting.
     */
    private function generateBlogContent(): string
    {
        $paragraphs = [];
        
        // Introduction paragraph
        $paragraphs[] = '<p>' . $this->faker->paragraph(4) . '</p>';
        
        // Add a heading and content section
        $paragraphs[] = '<h2>' . $this->faker->sentence(4, true) . '</h2>';
        $paragraphs[] = '<p>' . $this->faker->paragraph(6) . '</p>';
        
        // Add a list
        $listItems = [];
        for ($i = 0; $i < $this->faker->numberBetween(3, 5); $i++) {
            $listItems[] = '<li>' . $this->faker->sentence(6, true) . '</li>';
        }
        $paragraphs[] = '<ul>' . implode('', $listItems) . '</ul>';
        
        // Add another section
        $paragraphs[] = '<h3>' . $this->faker->sentence(3, true) . '</h3>';
        $paragraphs[] = '<p>' . $this->faker->paragraph(5) . '</p>';
        
        // Add a blockquote
        $paragraphs[] = '<blockquote><p>' . $this->faker->sentence(10, true) . '</p></blockquote>';
        
        // Add more content
        for ($i = 0; $i < $this->faker->numberBetween(2, 4); $i++) {
            $paragraphs[] = '<p>' . $this->faker->paragraph($this->faker->numberBetween(3, 7)) . '</p>';
        }
        
        // Add a conclusion
        $paragraphs[] = '<h3>Conclusion</h3>';
        $paragraphs[] = '<p>' . $this->faker->paragraph(4) . '</p>';
        
        return implode("\n\n", $paragraphs);
    }
}
