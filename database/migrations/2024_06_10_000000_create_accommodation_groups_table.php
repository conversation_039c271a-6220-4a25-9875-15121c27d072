<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accommodation_groups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('team_id')->nullable()->constrained()->onDelete('set null');
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });

        // Add group_id to accommodations table if it exists
        if (Schema::hasTable('accommodations')) {
            Schema::table('accommodations', function (Blueprint $table) {
                // Check if team_id exists, if not add after user_id, otherwise add after team_id
                if (Schema::hasColumn('accommodations', 'team_id')) {
                    $table->foreignId('accommodation_group_id')->after('team_id')->constrained('accommodation_groups');
                } else {
                    $table->foreignId('accommodation_group_id')->after('user_id')->constrained('accommodation_groups');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('accommodations') && Schema::hasColumn('accommodations', 'accommodation_group_id')) {
            Schema::table('accommodations', function (Blueprint $table) {
                $table->dropForeign(['accommodation_group_id']);
                $table->dropColumn('accommodation_group_id');
            });
        }

        Schema::dropIfExists('accommodation_groups');
    }
};
