<?php

namespace App\Filament\Resources;

use App\Filament\Resources\BlogResource\Pages;
use App\Models\Blog;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\MultiSelect;
use Filament\Tables\Columns\TagsColumn;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class BlogResource extends Resource
{
    protected static ?string $model = Blog::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?string $navigationGroup = 'Content Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Blog Post Details')
                    ->schema([
                        Forms\Components\TextInput::make('title')
                            ->required()
                            ->maxLength(255)
                            ->live(onBlur: true)
                            ->afterStateUpdated(function (string $context, $state, Forms\Set $set) {
                                if ($context === 'create') {
                                    $set('slug', Str::slug($state));
                                }
                            }),
                        
                        Forms\Components\TextInput::make('slug')
                            ->required()
                            ->maxLength(255)
                            ->unique(Blog::class, 'slug', ignoreRecord: true)
                            ->rules(['alpha_dash'])
                            ->helperText('URL-friendly version of the title'),
                        
                        Forms\Components\TextInput::make('subtitle')
                            ->maxLength(255)
                            ->helperText('Optional subtitle or description'),
                        
                        Forms\Components\DateTimePicker::make('post_date')
                            ->label('Publication Date')
                            ->helperText('Leave empty to set automatically when published'),
                        
                        Forms\Components\Toggle::make('published')
                            ->label('Published')
                            ->helperText('Make this blog post visible to the public'),
                        
                        MultiSelect::make('categories')
                            ->relationship('categories', 'name')
                            ->preload()
                            ->label('Categories'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Feature Image')
                    ->schema([
                        SpatieMediaLibraryFileUpload::make('feature_image')
                            ->collection('feature_image')
                            ->image()
                            ->maxFiles(1)
                            ->label('Feature Image')
                            ->helperText('Upload a feature image for this blog post (recommended size: 1200x630px)')
                            ->imageEditor()
                            ->imageEditorAspectRatios([
                                '16:9',
                                '4:3',
                                '1:1',
                            ]),
                    ]),

                Forms\Components\Section::make('Content')
                    ->schema([
                        Forms\Components\RichEditor::make('content')
                            ->required()
                            ->columnSpanFull()
                            ->toolbarButtons([
                                'attachFiles',
                                'blockquote',
                                'bold',
                                'bulletList',
                                'codeBlock',
                                'h2',
                                'h3',
                                'italic',
                                'link',
                                'orderedList',
                                'redo',
                                'strike',
                                'underline',
                                'undo',
                            ])
                            ->helperText('Write your blog post content here. You can format text, add links, and include images.'),
                    ]),

                Forms\Components\Section::make('SEO Settings')
                    ->description('Optimize your blog post for search engines')
                    ->schema([
                        Forms\Components\TextInput::make('meta_title')
                            ->label('Meta Title')
                            ->maxLength(60)
                            ->helperText('Custom title for search engines (max 60 characters). Leave empty to use the post title.')
                            ->placeholder('Enter a custom SEO title...')
                            ->suffixAction(
                                Forms\Components\Actions\Action::make('count')
                                    ->icon('heroicon-m-calculator')
                                    ->action(function () {})
                                    ->disabled()
                            ),

                        Forms\Components\Textarea::make('meta_description')
                            ->label('Meta Description')
                            ->maxLength(160)
                            ->rows(3)
                            ->helperText('Custom description for search engines (max 160 characters). Leave empty to use the subtitle or excerpt.')
                            ->placeholder('Enter a compelling description for search results...')
                            ->suffixAction(
                                Forms\Components\Actions\Action::make('count')
                                    ->icon('heroicon-m-calculator')
                                    ->action(function () {})
                                    ->disabled()
                            ),
                    ])
                    ->columns(1),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\SpatieMediaLibraryImageColumn::make('feature_image')
                    ->collection('feature_image')
                    ->width(80)
                    ->height(60),
                
                Tables\Columns\TextColumn::make('title')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                
                Tables\Columns\TextColumn::make('subtitle')
                    ->searchable()
                    ->limit(40)
                    ->toggleable(),
                
                Tables\Columns\IconColumn::make('published')
                    ->boolean()
                    ->sortable(),
                
                Tables\Columns\TextColumn::make('post_date')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(),
                
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Author')
                    ->sortable()
                    ->toggleable(),
                
                TagsColumn::make('categories.name')
                    ->label('Categories')
                    ->separator(', '),
                
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                
                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('published')
                    ->label('Publication Status')
                    ->boolean()
                    ->trueLabel('Published')
                    ->falseLabel('Draft')
                    ->native(false),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBlogs::route('/'),
            'create' => Pages\CreateBlog::route('/create'),
            'view' => Pages\ViewBlog::route('/{record}'),
            'edit' => Pages\EditBlog::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('user_id', auth()->id());
    }
}
