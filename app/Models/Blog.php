<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Blog extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'title',
        'slug',
        'subtitle',
        'content',
        'meta_title',
        'meta_description',
        'feature_image',
        'published',
        'post_date',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'published' => 'boolean',
        'post_date' => 'datetime',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function (Blog $blog) {
            if (empty($blog->slug)) {
                $blog->slug = Str::slug($blog->title);
            }
            
            // Ensure unique slug
            $originalSlug = $blog->slug;
            $counter = 1;
            while (static::where('slug', $blog->slug)->exists()) {
                $blog->slug = $originalSlug . '-' . $counter;
                $counter++;
            }

            // Set post_date to now if not set and published
            if ($blog->published && !$blog->post_date) {
                $blog->post_date = now();
            }
        });

        static::updating(function (Blog $blog) {
            // If title changed, update slug
            if ($blog->isDirty('title') && !$blog->isDirty('slug')) {
                $blog->slug = Str::slug($blog->title);
                
                // Ensure unique slug
                $originalSlug = $blog->slug;
                $counter = 1;
                while (static::where('slug', $blog->slug)->where('id', '!=', $blog->id)->exists()) {
                    $blog->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            // Set post_date when first published
            if ($blog->isDirty('published') && $blog->published && !$blog->post_date) {
                $blog->post_date = now();
            }
        });
    }

    /**
     * Get the user that owns the blog post.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the categories that belong to this blog post.
     */
    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(Category::class)->withTimestamps();
    }

    /**
     * Scope a query to only include published blog posts.
     */
    public function scopePublished($query)
    {
        return $query->where('published', true);
    }

    /**
     * Scope a query to order by post date descending.
     */
    public function scopeLatest($query)
    {
        return $query->orderBy('post_date', 'desc');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Register media collections for the model.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('feature_image')
            ->singleFile()
            ->useFallbackUrl('/images/blog-placeholder.jpg')
            ->useFallbackPath(public_path('/images/blog-placeholder.jpg'));

        $this->addMediaCollection('content_images')
            ->useFallbackUrl('/images/placeholder.jpg')
            ->useFallbackPath(public_path('/images/placeholder.jpg'));
    }

    /**
     * Get the feature image URL.
     */
    public function getFeatureImageUrlAttribute()
    {
        $media = $this->getFirstMedia('feature_image');
        return $media ? $media->getUrl() : $this->getFallbackMediaUrl('feature_image');
    }

    /**
     * Get excerpt from content.
     */
    public function getExcerptAttribute($length = 150)
    {
        return Str::limit(strip_tags($this->content), $length);
    }

    /**
     * Get reading time estimate.
     */
    public function getReadingTimeAttribute()
    {
        $wordCount = str_word_count(strip_tags($this->content));
        $readingTime = ceil($wordCount / 200); // Average reading speed: 200 words per minute
        return $readingTime . ' min read';
    }
}
