<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;

class BlogController extends Controller
{
    /**
     * Display a listing of published blog posts.
     */
    public function index(Request $request)
    {
        $categorySlug = $request->query('category');
        
        $query = Blog::published()->latest()->with('user', 'categories');
        if ($categorySlug) {
            $query->whereHas('categories', fn($q) => $q->where('slug', $categorySlug));
        }
        $blogs = $query->paginate(12);

        $allCategories = Category::orderBy('name')->get(['name', 'slug']);

        // Transform the data to include feature image URLs and categories
        $blogs->getCollection()->transform(function ($blog) {
            return [
                'id' => $blog->id,
                'title' => $blog->title,
                'slug' => $blog->slug,
                'subtitle' => $blog->subtitle,
                'excerpt' => $blog->excerpt,
                'feature_image_url' => $blog->feature_image_url,
                'post_date' => $blog->post_date,
                'reading_time' => $blog->reading_time,
                'author' => [
                    'name' => $blog->user->name,
                ],
                'categories' => $blog->categories->map(fn($c) => ['name' => $c->name, 'slug' => $c->slug]),
            ];
        });

        Inertia::setRootView('public');
        
        return Inertia::render('Public/Blog/Index', [
            'blogs' => $blogs,
            'categories' => $allCategories,
            'selectedCategory' => $categorySlug,
            'meta' => [
                'title' => 'Blog - BookingBear',
                'description' => 'Read the latest insights, tips, and updates from the BookingBear team about accommodation management and booking systems.',
            ],
        ]);
    }

    /**
     * Display the specified blog post.
     */
    public function show(Blog $blog)
    {
        // Only show published blog posts to public
        if (!$blog->published) {
            abort(404);
        }

        $blog->load('user', 'categories');

        // Get related blog posts (same author, excluding current post)
        $relatedBlogs = Blog::published()
            ->where('user_id', $blog->user_id)
            ->where('id', '!=', $blog->id)
            ->latest()
            ->limit(3)
            ->get()
            ->map(function ($relatedBlog) {
                return [
                    'id' => $relatedBlog->id,
                    'title' => $relatedBlog->title,
                    'slug' => $relatedBlog->slug,
                    'subtitle' => $relatedBlog->subtitle,
                    'excerpt' => $relatedBlog->excerpt,
                    'feature_image_url' => $relatedBlog->feature_image_url,
                    'post_date' => $relatedBlog->post_date,
                    'reading_time' => $relatedBlog->reading_time,
                ];
            });

        Inertia::setRootView('public');
        
        return Inertia::render('Public/Blog/Show', [
            'blog' => [
                'id' => $blog->id,
                'title' => $blog->title,
                'slug' => $blog->slug,
                'subtitle' => $blog->subtitle,
                'content' => $blog->content,
                'feature_image_url' => $blog->feature_image_url,
                'post_date' => $blog->post_date,
                'reading_time' => $blog->reading_time,
                'author' => [
                    'name' => $blog->user->name,
                ],
                'categories' => $blog->categories->map(fn($c) => ['name' => $c->name, 'slug' => $c->slug]),
            ],
            'relatedBlogs' => $relatedBlogs,
            'meta' => [
                'title' => $blog->meta_title ?: ($blog->title . ' - BookingBear Blog'),
                'description' => $blog->meta_description ?: ($blog->subtitle ?: $blog->excerpt),
                'image' => $blog->feature_image_url,
            ],
        ]);
    }
}
