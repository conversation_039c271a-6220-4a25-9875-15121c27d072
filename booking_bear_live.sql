-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jul 07, 2025 at 07:54 AM
-- Server version: 8.0.42-0ubuntu0.24.04.1
-- PHP Version: 8.4.7

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `booking_bear_live`
--

-- --------------------------------------------------------

--
-- Table structure for table `accommodations`
--

CREATE TABLE `accommodations` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED DEFAULT NULL,
  `accommodation_group_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `province` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `post_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `country` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `published` tinyint(1) NOT NULL DEFAULT '1',
  `min_occupancy` int UNSIGNED NOT NULL DEFAULT '1',
  `max_occupancy` int UNSIGNED NOT NULL DEFAULT '2',
  `minimum_booking_notice` int UNSIGNED NOT NULL DEFAULT '0' COMMENT 'Minimum number of days in advance a booking can be made',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `minimum_stay` int NOT NULL DEFAULT '1'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodations`
--

INSERT INTO `accommodations` (`id`, `user_id`, `team_id`, `accommodation_group_id`, `name`, `description`, `address`, `city`, `province`, `post_code`, `country`, `published`, `min_occupancy`, `max_occupancy`, `minimum_booking_notice`, `created_at`, `updated_at`, `deleted_at`, `minimum_stay`) VALUES
(1, 1, 1, 1, 'Koss, Heller and Schimmel', 'Odit ut culpa repudiandae impedit autem modi odit. Laborum repudiandae aut impedit esse.', '5932 Merritt Parkway\nBaumbachberg, KY 65245-3350', 'Emmaleeview', 'New Hampshire', '56415-4600', 'Turkmenistan', 1, 2, 4, 1, '2025-05-23 08:52:22', '2025-06-12 08:18:14', '2025-06-12 08:18:14', 1),
(2, 1, 1, 1, 'Pfannerstill, Graham and Hessel', 'Dolorum expedita ut ad. Voluptas quam possimus voluptate ut quia iste. Voluptatem ea sit cumque vel qui voluptas voluptatem. At quae quam nam autem deleniti rerum consequatur.', '6777 Thiel Street Suite 397\nEveburgh, HI 75101-0526', 'Laurencemouth', 'Nebraska', '25628', 'Israel', 1, 1, 6, 5, '2025-05-23 08:52:22', '2025-06-12 08:18:17', '2025-06-12 08:18:17', 1),
(3, 1, 1, 6, 'Rogahn, Treutel and Krajcik', 'Est et dolor alias enim fugit et. Est veniam quia sunt. Sint accusantium odio dicta totam voluptas.', '96035 Anika Street\nNew Nikkiton, NE 26323', 'East Robert', 'Oregon', '30027', 'Rwanda', 0, 1, 6, 7, '2025-05-23 08:52:22', '2025-06-24 11:39:27', '2025-06-12 08:18:20', 1),
(4, 1, 1, 6, 'Greenfelder, Green and Mann', 'Explicabo soluta sunt hic consequuntur. Officiis deleniti occaecati sint dolore. Ipsum nesciunt odio quae omnis non reprehenderit.', '4391 Barton Ville\nNew Jeffery, OH 51836-6986', 'Bradlyhaven', 'Virginia', '59456-3046', 'Guam', 0, 2, 6, 2, '2025-05-23 08:52:22', '2025-06-24 11:39:27', '2025-06-12 08:18:23', 1),
(5, 1, 1, 6, 'Bahringer-Gusikowski', 'Enim pariatur dolores expedita autem. Dicta quae ut numquam ut.', '61486 Cheyanne Flats\nNorth Taya, NM 57507', 'Pacochashire', 'Wisconsin', '17360', 'Colombia', 0, 2, 6, 2, '2025-05-23 08:52:22', '2025-06-24 11:39:27', '2025-06-12 08:18:25', 1),
(6, 4, 4, 2, 'My Home', 'This is my home', '15 Crescent Road', 'Cape Town', 'Western Cape', '7800', 'South Africa', 1, 3, 4, 2, '2025-05-23 13:28:44', '2025-06-24 11:53:38', '2025-06-24 11:53:38', 1),
(7, 5, 5, 3, 'Bay 1', 'This is bay 1', '301 forest road', 'Benoni', 'Gauteng', '1234', 'South Africa', 0, 1, 2, 2, '2025-05-27 09:32:29', '2025-05-27 09:32:29', NULL, 1),
(8, 5, 5, 3, 'Bay 2', 'This is bay 2', '301 forest road', 'Johannesburg', 'Gauteng', '2196', 'South Africa', 0, 1, 2, 2, '2025-05-27 09:41:13', '2025-05-27 09:41:13', NULL, 1),
(9, 1, 1, 4, 'Honeydrop Hollow Cabin', 'Tucked beneath a canopy of ancient yellowwoods, this cozy timber cabin is the perfect hideaway for nature-loving bears (and humans too). Comes with a porch made for sipping rooibos and judging squirrels.', 'Knysna Forest, Western Cape', 'Kysna', 'Western Cape', '6570', 'South Africa', 1, 1, 2, 2, '2025-06-12 08:20:49', '2025-06-12 10:44:58', NULL, 1),
(10, 1, 1, 4, 'The Sleepy Den Lodge', 'A misty mountain lodge with stone walls, crackling fires, and oversized couches for post-hibernation naps. Ideal for lazy weekends and woodland wanderings.', 'Hogsback', 'Hogsback', 'Eastern Cape', '9999', 'South Africa', 1, 2, 5, 2, '2025-06-12 08:24:38', '2025-06-12 10:45:07', NULL, 2),
(11, 1, 1, 4, 'Pinecone Ridge Retreat', 'Remote, peaceful, and perched above a valley full of birdsong. If you\'re the type of bear who likes stargazing, solitude, and slow-brewing coffee, this is your spot.', 'Cederberg Mountains', 'Cederberg', 'Western Cape', '9999', 'South Africa', 1, 2, 4, 3, '2025-06-12 08:26:33', '2025-06-12 10:45:14', NULL, 1),
(12, 1, 1, 5, 'The Sandy Paw Chalet', 'This bright, surfside chalet is ideal for bears who enjoy long walks on the beach and short waddles to the nearest gelato stand. Surfboard rack included. Hibernation optional.', 'The Beachfront', 'Muizenberg', 'Western Cape', '7800', 'South Africa', 1, 1, 2, 3, '2025-06-12 08:29:49', '2025-06-12 10:45:22', NULL, 3),
(13, 1, 1, 5, 'Bearfoot Bungalow', 'Steps from the beach and shaded by milkwood trees, this breezy bungalow has all the essentials: hammocks, coastal sunsets, and a fully-stocked honey jar shelf. BYO sunglasses.', 'On the Beach', 'Wilderness, Garden Route', 'Western Cape', '9999', 'South Africa', 1, 2, 10, 2, '2025-06-12 08:32:04', '2025-06-12 10:45:29', NULL, 2);

-- --------------------------------------------------------

--
-- Table structure for table `accommodation_groups`
--

CREATE TABLE `accommodation_groups` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodation_groups`
--

INSERT INTO `accommodation_groups` (`id`, `user_id`, `team_id`, `name`, `description`, `published`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 1, 'commodi nostrum', 'Quasi laborum consequatur vel omnis repudiandae qui ab. Unde facere asperiores et porro. Qui corrupti blanditiis non commodi eveniet.', 1, '2025-05-23 08:52:22', '2025-06-12 08:18:35', '2025-06-12 08:18:35'),
(2, 4, 4, 'Close', 'Places close to me', 1, '2025-05-23 13:29:39', '2025-06-24 11:53:44', '2025-06-24 11:53:44'),
(3, 5, 5, 'East Rand', 'The group for the east rand bookings', 1, '2025-05-27 09:31:02', '2025-05-27 09:31:11', NULL),
(4, 1, 1, 'Forest Hideaways', 'Rustic, nature-loving bear retreats—think mountain cabins, river lodges, and forest escapes.', 1, '2025-06-12 08:18:05', '2025-06-12 13:53:32', NULL),
(5, 1, 1, 'Coastal Bears & Beachfront Naps', 'Relaxed coastal getaways with a wink—perfect for sun-loving, sand-between-the-toes bears.', 1, '2025-06-12 08:28:35', '2025-06-12 13:37:49', NULL),
(6, 1, 1, 'Default Group', 'Automatically created group for existing accommodations', 1, '2025-06-24 11:39:27', '2025-06-24 11:39:27', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `accommodation_group_prices`
--

CREATE TABLE `accommodation_group_prices` (
  `id` bigint UNSIGNED NOT NULL,
  `accommodation_group_id` bigint UNSIGNED NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `additional_person_price` decimal(10,2) NOT NULL DEFAULT '0.00',
  `type` enum('default','date_range','day_of_week') COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `day_of_week` int DEFAULT NULL,
  `priority` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodation_group_prices`
--

INSERT INTO `accommodation_group_prices` (`id`, `accommodation_group_id`, `price`, `additional_person_price`, `type`, `start_date`, `end_date`, `day_of_week`, `priority`, `created_at`, `updated_at`) VALUES
(1, 2, 20000.00, 10000.00, 'default', NULL, NULL, NULL, 0, '2025-05-23 13:30:52', '2025-05-23 13:30:52'),
(2, 1, 40000.00, 30000.00, 'default', NULL, NULL, NULL, 0, '2025-06-06 07:59:38', '2025-06-06 07:59:38'),
(3, 4, 120000.00, 40000.00, 'default', NULL, NULL, NULL, 0, '2025-06-12 08:34:11', '2025-06-12 08:34:11'),
(4, 5, 85000.00, 30000.00, 'default', NULL, NULL, NULL, 0, '2025-06-12 08:34:32', '2025-06-12 08:34:32');

-- --------------------------------------------------------

--
-- Table structure for table `accommodation_prices`
--

CREATE TABLE `accommodation_prices` (
  `id` bigint UNSIGNED NOT NULL,
  `accommodation_id` bigint UNSIGNED NOT NULL,
  `price` decimal(10,2) NOT NULL,
  `type` enum('default','date_range','day_of_week') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `day_of_week` int DEFAULT NULL,
  `priority` int NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `additional_person_price` decimal(10,2) NOT NULL DEFAULT '0.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodation_prices`
--

INSERT INTO `accommodation_prices` (`id`, `accommodation_id`, `price`, `type`, `start_date`, `end_date`, `day_of_week`, `priority`, `created_at`, `updated_at`, `additional_person_price`) VALUES
(1, 7, 200000.00, 'default', NULL, NULL, NULL, 0, '2025-05-27 09:43:54', '2025-05-27 09:43:54', 50000.00),
(2, 8, 200000.00, 'default', NULL, NULL, NULL, 0, '2025-05-27 09:44:55', '2025-05-27 09:44:55', 50000.00),
(3, 10, 150000.00, 'day_of_week', NULL, NULL, 5, 0, '2025-06-12 08:35:16', '2025-06-12 08:35:16', 40000.00),
(4, 13, 95000.00, 'default', NULL, NULL, NULL, 0, '2025-06-12 08:35:39', '2025-06-12 08:35:39', 35000.00);

-- --------------------------------------------------------

--
-- Table structure for table `accommodation_searches`
--

CREATE TABLE `accommodation_searches` (
  `id` bigint UNSIGNED NOT NULL,
  `accommodation_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `requested_occupancy` int DEFAULT NULL,
  `was_available` tinyint(1) NOT NULL,
  `unavailability_reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `quoted_price` decimal(10,2) DEFAULT NULL,
  `searched_at` timestamp NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodation_searches`
--

INSERT INTO `accommodation_searches` (`id`, `accommodation_id`, `user_id`, `start_date`, `end_date`, `requested_occupancy`, `was_available`, `unavailability_reason`, `quoted_price`, `searched_at`) VALUES
(1, 1, 1, '2025-07-02', '2025-07-04', 2, 1, NULL, 800.00, '2025-06-06 08:05:12'),
(2, 1, 1, '2025-07-01', '2025-07-07', 2, 0, 'blocked_period', NULL, '2025-06-06 08:05:19'),
(3, 1, 1, '2025-07-01', '2025-07-03', 2, 1, NULL, 800.00, '2025-06-06 09:49:44'),
(4, 9, 1, '2025-06-12', '2025-06-14', 1, 0, 'minimum_notice', NULL, '2025-06-12 11:27:35'),
(5, 9, 1, '2025-06-12', '2025-06-14', 1, 0, 'minimum_notice', NULL, '2025-06-12 11:31:08'),
(6, 9, 1, '2025-06-14', '2025-06-16', 1, 1, NULL, 2400.00, '2025-06-12 11:31:20'),
(7, 9, 1, '2025-06-14', '2025-06-16', 2, 1, NULL, 3200.00, '2025-06-12 11:31:33'),
(8, 13, 1, '2025-06-12', '2025-06-13', 2, 0, 'minimum_notice', NULL, '2025-06-12 11:35:09'),
(9, 13, 1, '2025-07-21', '2025-07-22', 2, 0, 'minimum_stay', NULL, '2025-06-12 11:35:17'),
(10, 13, 1, '2025-07-21', '2025-07-23', 2, 1, NULL, 1900.00, '2025-06-12 11:35:25'),
(11, 10, 1, '2025-07-28', '2025-07-31', 2, 1, NULL, 3600.00, '2025-06-12 11:37:53'),
(12, 9, 1, '2025-06-28', '2025-06-29', 1, 1, NULL, 1200.00, '2025-06-17 13:49:05'),
(13, 13, 1, '2025-06-19', '2025-06-22', 2, 1, NULL, 2850.00, '2025-06-17 13:49:32'),
(14, 9, 1, '2025-07-15', '2025-07-17', 1, 1, NULL, 2400.00, '2025-06-18 13:54:33');

-- --------------------------------------------------------

--
-- Table structure for table `accommodation_unavailable_periods`
--

CREATE TABLE `accommodation_unavailable_periods` (
  `id` bigint UNSIGNED NOT NULL,
  `accommodation_id` bigint UNSIGNED NOT NULL,
  `type` enum('date_range','recurring_days') COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `days_of_week` json DEFAULT NULL,
  `reason` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `accommodation_unavailable_periods`
--

INSERT INTO `accommodation_unavailable_periods` (`id`, `accommodation_id`, `type`, `start_date`, `end_date`, `days_of_week`, `reason`, `active`, `created_at`, `updated_at`) VALUES
(1, 1, 'recurring_days', NULL, NULL, '[1]', NULL, 1, '2025-06-06 08:04:20', '2025-06-06 08:04:20'),
(2, 9, 'recurring_days', NULL, NULL, '[1]', 'Cleaning', 1, '2025-06-17 13:48:41', '2025-06-17 13:48:41');

-- --------------------------------------------------------

--
-- Table structure for table `blogs`
--

CREATE TABLE `blogs` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subtitle` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `feature_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `published` tinyint(1) NOT NULL DEFAULT '0',
  `post_date` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blogs`
--

INSERT INTO `blogs` (`id`, `user_id`, `title`, `slug`, `subtitle`, `content`, `feature_image`, `published`, `post_date`, `created_at`, `updated_at`) VALUES
(1, 1, 'Strategic Pricing for Small Accommodation Operators: Maximising Revenue in the Western Cape Market', 'strategic-pricing-for-small-accommodation-operators', 'Running a successful guest house, guest farm, or campsite in South Africa\'s competitive accommodation landscape requires more than just providing comfortable beds and beautiful views. Your pricing strategy can make the difference between thriving during p', '<p>Your pricing strategy can make the difference between thriving during peak seasons and struggling through quieter months.</p><p>With the Western Cape experiencing a remarkable recovery in tourism—tourist arrivals to Cape Town by air recorded a remarkable 16% year-on-year increase between January and March 2024, reaching 336,268 visitors—now is the time to refine your pricing approach to capitalise on this growth while building sustainable revenue streams throughout the year.</p><h2>Understanding Your Market Position</h2><p>Before setting rates, you need to understand where your property fits within the broader accommodation ecosystem. The South African market spans from budget-conscious domestic travellers to international tourists seeking authentic experiences.</p><p>For campsites, rates typically start around R150 per night for basic facilities, while guest houses and self-catering accommodations generally command R750 and upward. However, these baseline figures only tell part of the story. Your actual pricing should reflect your unique value proposition, location advantages, and target market preferences.</p><p><strong>Location Premium Factors:</strong></p><ul><li>Proximity to major attractions (Table Mountain, wine routes, beaches)</li><li>Accessibility to transport hubs</li><li>Neighbourhood safety and reputation</li><li>Local amenities and activities</li></ul><h2>Mastering Seasonal Pricing Dynamics</h2><p>The Western Cape\'s tourism patterns create distinct opportunities for strategic pricing throughout the year. Understanding these cycles allows you to maximise revenue during peak periods while maintaining occupancy during slower months.</p><h3>Peak Season Strategy (December - January)</h3><p>December holidays represent your highest earning potential, but avoid the temptation to simply double your rates. Research shows that moderate premium pricing (30-50% above base rates) often generates more total revenue than aggressive increases that deter bookings.</p><p><strong>Peak Season Tactics:</strong></p><ul><li>Implement minimum stay requirements (3-7 nights)</li><li>Introduce early booking incentives for the following year</li><li>Create package deals that bundle accommodation with local experiences</li><li>Consider differential pricing for weekends versus weekdays, even during peak season</li></ul><h3>Shoulder Season Opportunities (February-March, October-November)</h3><p>These months offer excellent potential for guest houses and guest farms. International visitors often prefer these periods for better weather and smaller crowds, while domestic tourists take advantage of post-holiday deals.</p><p>Price your shoulder seasons at 10-20% below peak rates while maintaining your value proposition. This approach attracts price-sensitive travellers without devaluing your property.</p><h3>Low Season Innovation (May-September)</h3><p>May to September is traditionally when occupancy levels dip to their lowest levels and these are the months that require urgent attention. Rather than simply dropping prices, consider creative approaches:</p><ul><li><strong>Local Market Focus:</strong> Target domestic business travellers and weekend getaways</li><li><strong>Extended Stay Discounts:</strong> Offer weekly or monthly rates for digital nomads and relocating professionals</li><li><strong>Event-Driven Pricing:</strong> Align with local festivals, conferences, or sporting events</li><li><strong>Maintenance Partnerships:</strong> Offer discounted rates to contractors and suppliers during property improvement periods</li></ul><h2>Competitive Intelligence Without Compromise</h2><p>Understanding your competition doesn\'t mean engaging in price wars. Instead, focus on identifying gaps in the market where you can position your property advantageously.</p><p><strong>Research Framework:</strong></p><ol><li><strong>Direct Competitors:</strong> Properties within 5km offering similar amenities and target markets</li><li><strong>Substitute Options:</strong> Alternative accommodation types (hotels, self-catering, other guest houses) that serve your customer base</li><li><strong>Value Differentiators:</strong> Unique features that justify premium pricing</li></ol><p>Monitor competitor pricing weekly during peak booking periods (6-8 weeks before peak seasons) and monthly during quieter times. However, avoid reactive pricing changes that can signal desperation to potential guests.</p><h2>Dynamic Pricing Principles for Small Operators</h2><p>While large hotel chains use sophisticated revenue management systems, small accommodation operators can implement effective dynamic pricing using accessible tools and strategies.</p><p><strong>Core Pricing Variables:</strong></p><ul><li><strong>Advance Booking Period:</strong> Prices 60+ days ahead versus last-minute bookings</li><li><strong>Length of Stay:</strong> Incentivise longer bookings with progressive discounts</li><li><strong>Group Size:</strong> Family rates versus individual traveller pricing</li><li><strong>Day of Week:</strong> Weekend premiums in leisure destinations</li><li><strong>Local Events:</strong> Conference pricing, festival premiums, sporting event rates</li></ul><p><strong>Practical Implementation:</strong> Set three price tiers for each season: Standard, Premium (high-demand periods), and Promotional (filling gaps). This gives you flexibility without requiring daily rate changes.</p><h2>Revenue Optimisation Beyond Room Rates</h2><p>Diversified revenue streams can significantly impact your bottom line while providing added value to guests.</p><p><strong>Additional Revenue Opportunities:</strong></p><ul><li><strong>Experience Packages:</strong> Partner with local activity providers for commission-based offerings</li><li><strong>Extended Services:</strong> Airport transfers, grocery provisioning, equipment rental</li><li><strong>Event Hosting:</strong> Small weddings, corporate retreats, photography sessions</li><li><strong>Seasonal Offerings:</strong> Harvest experiences on guest farms, braai equipment rental for campsites</li></ul><p>These services often carry higher profit margins than accommodation alone and create memorable experiences that encourage repeat visits and positive reviews.</p><h2>Technology Tools for Pricing Success</h2><p>Modern accommodation management requires strategic use of technology, but you don\'t need enterprise-level solutions to implement effective pricing strategies.</p><p><strong>Essential Tools:</strong></p><ul><li><strong>Channel Management:</strong> Maintain consistent pricing across booking platforms</li><li><strong>Rate Shopping:</strong> Monitor competitor pricing efficiently</li><li><strong>Booking Analytics:</strong> Track your booking patterns and price sensitivity</li><li><strong>Calendar Management:</strong> Visualise occupancy and pricing across seasons</li></ul><p>The key is selecting tools that integrate well with your existing operations rather than creating additional administrative burden.</p><h2>Measuring Success and Making Adjustments</h2><p>Effective pricing strategy requires ongoing measurement and refinement. Focus on metrics that matter for small accommodation operators:</p><p><strong>Key Performance Indicators:</strong></p><ul><li><strong>Revenue Per Available Room (RevPAR):</strong> Total room revenue divided by available room nights</li><li><strong>Average Daily Rate (ADR):</strong> Total room revenue divided by rooms sold</li><li><strong>Occupancy Rate:</strong> Percentage of available rooms occupied</li><li><strong>Lead Time:</strong> Average days between booking and arrival</li></ul><p><strong>Monthly Review Process:</strong></p><ol><li>Analyse booking patterns and revenue performance</li><li>Compare actual results to seasonal expectations</li><li>Identify pricing opportunities for the following month</li><li>Adjust rates based on booking pace and competitive landscape</li></ol><h2>Common Pricing Pitfalls to Avoid</h2><p>Many small accommodation operators fall into predictable pricing traps that limit their revenue potential:</p><p><strong>Underpricing During Peak Demand:</strong> Fear of losing bookings often leads to leaving money on the table during high-demand periods. If you\'re fully booked weeks in advance, your rates are likely too low.</p><p><strong>Inconsistent Pricing Across Channels:</strong> Different rates on various booking platforms confuse customers and can violate platform agreements.</p><p><strong>Emotional Pricing Decisions:</strong> Setting rates based on personal financial pressures rather than market conditions often backfires.</p><p><strong>Ignoring Local Market Conditions:</strong> Failing to adjust for local events, weather patterns, or economic conditions misses revenue opportunities.</p><h2>Building Long-Term Pricing Strategy</h2><p>Sustainable pricing goes beyond maximising short-term revenue. Consider the long-term impact of your pricing decisions on brand perception, customer loyalty, and market positioning.</p><p><strong>Strategic Considerations:</strong></p><ul><li><strong>Brand Positioning:</strong> Ensure pricing aligns with your property\'s market position</li><li><strong>Customer Lifetime Value:</strong> Balance immediate revenue with repeat booking potential</li><li><strong>Market Development:</strong> Consider your role in developing local tourism demand</li><li><strong>Operational Sustainability:</strong> Ensure pricing supports property maintenance and improvements</li></ul><h2>Adapting to Market Changes</h2><p>The accommodation industry continues evolving, with changing traveller preferences, economic conditions, and competitive dynamics. The Western Cape tourism sector is experiencing a strong rebound, with international arrivals increasing by 6.8% in 2024, indicating positive momentum for strategic operators.</p><p>Stay agile by:</p><ul><li>Monitoring booking trends and guest feedback</li><li>Adapting to new distribution channels and booking behaviours</li><li>Responding to economic conditions affecting your target markets</li><li>Investing in property improvements that justify pricing premiums</li></ul><h2>Conclusion</h2><p>Effective pricing strategy for small accommodation operators combines market intelligence, seasonal awareness, and operational flexibility. Success comes not from finding the perfect price, but from developing systematic approaches that adapt to changing conditions while maintaining your property\'s value proposition.</p><p>The Western Cape\'s tourism recovery presents significant opportunities for accommodation operators willing to move beyond basic pricing approaches. By implementing strategic pricing principles, leveraging available technology, and maintaining focus on guest value, small operators can compete effectively while building sustainable businesses.</p><p>Remember that pricing is just one element of your overall guest experience. The most successful accommodation operators combine strategic pricing with exceptional service, creating value that guests willingly pay for and eagerly recommend to others.</p><p>Your pricing strategy should reflect your property\'s unique strengths while remaining responsive to market conditions. Start with these principles, measure your results, and refine your approach based on real booking data and guest feedback. The investment in strategic pricing will pay dividends through improved occupancy, higher revenue, and stronger business sustainability.</p><p>If you\'d like some direction on pricing, why not try our free <a href=\"/tools/pricing-calculator\">Accommodation Pricing Calculator</a></p><p><em>Ready to implement these pricing strategies for your accommodation business? The right management tools can streamline your pricing decisions and booking operations, giving you more time to focus on creating exceptional guest experiences.</em></p><p><br></p>', NULL, 1, '2025-06-02 11:14:18', '2025-06-02 09:14:51', '2025-06-02 10:41:24'),
(2, 1, '5 Reasons Guests Leave Your Site Without Booking—And How to Fix It', '5-reasons-guests-leave-your-site-without-booking', 'You’ve Got the Guesthouse, the Website, the Traffic… So Why No Bookings?', '<p>You’ve done the hard part.<br>&nbsp;You’ve poured love (and a fair chunk of money) into your guesthouse. You’ve got a website up and running, and people are <em>actually</em> visiting it. So why aren\'t they clicking that <em>“Book Now”</em> button?</p><p>If you’re scratching your head wondering <em>why guests don’t book your accommodation</em>—you’re not alone. Many accommodation owners face this exact frustration.</p><p>Here’s the good news: most of the time, the problem isn’t your guesthouse. It’s your <em>online experience</em>. Small issues on your website—ones you might not even notice—can make a big difference to whether a guest books or bounces.</p><p>This post walks through five of the most common reasons potential guests leave without booking (and more importantly, how to fix them). Whether you\'re running a B&amp;B in Montague or a seaside stay in Paternoster, these tips are for you.</p><h2>1. Your Booking Process Is Clunky or Missing</h2><p><strong>The problem:</strong> Your website doesn’t have an obvious, easy way to book. Maybe there’s just a phone number. Or a contact form that feels more like a prayer request than a booking engine.</p><p><strong>Why this hurts:</strong> Guests today expect instant gratification. They want to check dates, prices, and availability <em>right now</em>—not wait for an email reply or phone call. If your site doesn’t offer that, they’ll find one that does.</p><p><strong>The fix:</strong><br> Add a clear, user-friendly booking widget directly to your site. Tools like <a href=\"https://www.bookingbear.co.za\"><span style=\"text-decoration: underline;\">Booking Bear</span></a> make it easy to embed a simple calendar where guests can check availability and submit a booking request—without ever leaving your site.</p><p>Even better? A tool like this can help you <strong>increase direct bookings for your guesthouse</strong> by cutting out third-party platforms that take a slice of your revenue.</p><blockquote><strong>Pro tip:</strong> Put your “Book Now” button in your top menu <em>and</em> again lower on the page, where people are most likely to be convinced.</blockquote><h2>2. Your Site Doesn’t Look Trustworthy</h2><p><strong>The problem:</strong> Your website looks like it time-travelled from 2009. Or maybe there are broken links, outdated rates, or fuzzy photos. Maybe your “About Us” section still says your guesthouse is under construction (oops).</p><p><strong>Why this hurts:</strong> Guests don’t want to risk their holiday (or their credit card info) on something that looks sketchy. First impressions count—a lot.</p><p><strong>The fix:</strong><br> A few small tweaks can make a world of difference:</p><ul><li>Replace blurry photos with high-quality images of your rooms and property.</li><li>Add real guest reviews (even if they’re just copied from WhatsApp or Google).</li><li>Double-check that all your links, prices, and contact info are current.</li><li>Use friendly, conversational copy that sounds like <em>you</em>—not a dusty hotel chain.</li></ul><p>A clean, trustworthy site isn’t just pretty—it converts better. This is one of the most practical <strong>accommodation booking website tips</strong> you can act on today.</p><h2>3. No Mobile Optimization</h2><p><strong>The problem:</strong> Your site looks fine on desktop… but falls apart on a phone. Tiny buttons. Long load times. Calendars that don’t scroll. Booking details that are hidden five clicks deep.</p><p><strong>Why this hurts:</strong> Depending on your audience, <em>up to 70% of your traffic</em> is likely visiting from a mobile device. If the experience is painful, they’re gone.</p><p><strong>The fix:</strong><br> Make sure your site is mobile-friendly. That means:</p><ul><li>Responsive design that adjusts beautifully to all screen sizes.</li><li>Easy-to-tap buttons (fat thumbs, remember?).</li><li>Fast load speeds (compress those images!).</li><li>Booking info front and centre—don’t make users hunt for it.</li></ul><blockquote><strong>Quick test:</strong> Open your website on your phone <em>right now</em>. Can you book in under 30 seconds? If not, it’s time for a tune-up.</blockquote><h2>4. No Real-Time Availability or Pricing Info</h2><p><strong>The problem:</strong> Visitors can’t see when your guesthouse is available or how much it’ll cost them. Maybe you list prices as “from R800,” or your calendar hasn’t been updated since Christmas.</p><p><strong>Why this hurts:</strong> If guests don’t know when they can stay—or what it’ll cost—they’re likely to leave your site and book somewhere with clearer info. It’s not personal; it’s just convenience.</p><p><strong>The fix:</strong><br> Use a booking tool that shows real-time availability and accurate pricing. Bonus points if it can handle seasonal rates, discounts, and occupancy-based pricing.</p><p>Booking Bear, for example, makes this easy and affordable—even for small guesthouses without tech teams. And showing this info directly helps <strong>increase direct bookings for guesthouses</strong>, since guests don’t have to guess or email.</p><h2>5. They Got Distracted or Unconvinced</h2><p><strong>The problem:</strong> A potential guest landed on your site… and got overwhelmed. Maybe your homepage didn’t grab them. Or there was no clear reason to book <em>right now</em>. Or they got distracted by Instagram and never came back.</p><p><strong>Why this hurts:</strong> Most users don’t decide immediately. If your site doesn’t give them a reason to stay, explore, and book, you’ll lose them.</p><p><strong>The fix:</strong></p><ul><li>Use strong, clear calls to action like “Book your stay now” or “Check availability.”</li><li>Highlight what makes you special—views, location, homemade rusks, dog-friendly policy?</li><li>Offer direct booking perks: free breakfast, a bottle of wine, early check-in.</li><li>Add urgency with messages like “Only 2 weekends left in July!”</li></ul><p>These aren’t tricks—they’re ways to help guests feel confident and excited to book with you.</p><h2>Let’s Recap (Because We’re All Busy)</h2><p>If you’re wondering <em>why guests don’t book your accommodation</em>, it usually comes down to friction.<br> Not enough info. Not enough trust. Not enough clarity.</p><p>The good news? Fixing even <em>one or two</em> of these issues can make a noticeable difference in your conversion rate.</p><ul><li>Make your booking process easy and instant.</li><li>Build trust with clean design and real reviews.</li><li>Ensure mobile visitors have a great experience.</li><li>Show real-time availability and accurate pricing.</li><li>Give guests a nudge with urgency and perks.</li></ul><h2>Ready to Start Fixing These Issues?</h2><p>Want a lightweight booking tool that doesn’t hijack your brand?<br><strong>Try </strong><a href=\"https://www.bookingbear.co.za/register\"><strong>Booking Bear</strong></a><strong> free for</strong> and start turning browsers into bookers.</p><p>We’re here to help you get those rooms filled—without the tech headaches.</p>', NULL, 1, '2025-06-09 19:30:41', '2025-06-09 17:46:53', '2025-06-24 13:37:07'),
(3, 1, 'Minimum Night Stays: Smart Strategy or Missed Opportunity?', 'minimum-night-stays-smart-strategy-or-missed-opportunity', 'Many South African accommodation owners set 2- or 3-night minimums to save time and reduce cleaning—but are they missing out on valuable short-stay guests?', '<p>&nbsp;Independent accommodation owners often wonder: <strong>“Should I allow 1‑night bookings?”</strong> It’s a common conundrum—minimum night stay rules can protect your property and sanity, but could also be costing you revenue and guests. In this article, we\'ll break down both sides of the issue—and show how flexible strategies can help you stay competitive—all while highlighting how <em>Booking Bear</em> makes it simpler.</p><p>By the end, you’ll understand where minimum stay rules make sense, when they’re holding you back, and how to experiment without risking bookings. Let’s dive in!</p><h2><strong>What Is a Minimum Night Stay Policy?</strong></h2><p><strong>Simple Definition</strong><br> A minimum night stay policy means guests must book for at least a certain number of nights—say 2 or 3—to reserve your property.</p><p><strong>Common Real‑Life Examples</strong></p><ul><li>A <strong>2‑night minimum</strong> over weekends</li><li>A <strong>3‑night rule</strong> in peak season (e.g., December or Easter)</li><li>No minimum during low season</li></ul><p>Many hosts add these to reduce turnover during busy times, but it can be overly restrictive if auto‑applied year‑round.</p><h2><strong>Benefits of Setting Minimum Night Stays</strong></h2><p><strong>Less Frequent Turnover &amp; Easier Cleaning</strong><br> Fewer check‑ins means cleaning isn’t constant. For solo hosts or small teams, this saves time and energy.</p><p><strong>Higher Income Per Booking</strong><br> With longer stays, average nightly rate stays consistent, but you get more total nights per guest—which can boost income.</p><p><strong>Reduced Burnout for Hosts</strong><br> Constant cleaning and turnover is exhausting. Minimum nights let you schedule downtime, reducing fatigue.</p><p><strong>Streamlined Weekend &amp; Peak Management</strong><br> If weekends get busy, a minimum stay ensures guests book full weekends rather than chopping up blocks—but only <em>if</em> demand is there.</p><h2><strong>The Downsides (Missed Opportunities)</strong></h2><p><strong>Losing Short‑Stay, One‑Night Guests</strong><br> Single‑night travellers—couples, business guests, or last‑minute planners—may hit “minimum stay” and move on. That’s lost income.</p><p><strong>Lower Search Visibility on OTAs</strong><br> Platforms often filter out properties with 1‑night openings. If you don’t allow short stays at all, you might drop in search results.</p><p><strong>Quiet Low‑Season Risk</strong><br> During off‑peak times, any booking is good. Blocking one‑nighters can result in empty nights you could easily fill.</p><p><strong>Short Stays Can Turn Into Return Bookings</strong><br> A 1‑night guest might return later for 3+ nights if they enjoy the place. Don’t dismiss them entirely.</p><h2><strong>How to Choose the Right Approach</strong></h2><ol><li><strong>Location &amp; Guest Profile</strong><ul><li>Coastal/vineyard retreats may attract weekenders—so minimum nights on weekends make sense</li><li>Business‑traveller areas may benefit from more 1‑ or 2‑night flexibility</li></ul></li><li><strong>Turnover Capacity</strong><br> Calculate how many turnovers you can realistically manage weekly without burnout.</li><li><strong>Willingness to Experiment</strong><br> Booking Bear lets you test strategies by adjusting stay rules easily—ideal for learning what works for your place.</li></ol><h2><strong>Smart Compromise Strategies</strong></h2><p><strong>Weekend/Holiday Only Minimums</strong><br> Require 2–3 nights on weekends or public holidays, but allow flexibility rest of the week.</p><p><strong>Weekday or Off‑Peak Flexibility</strong><br> Low‑season weeks can stay wide open for 1‑night guests to help fill the calendar.</p><p><strong>Last‑Minute One‑Night Opens</strong><br> Open up remaining nights within 72 hours, giving tools for cancellations or last‑minute travelers.</p><p><strong>Long‑Stay Discounts Over Restrictions</strong><br> Offer a 10–15% discount for guests staying 3+ nights instead of forcing minimum stays—gently encouraging longer bookings without blocking one-nighters.</p><h2><strong>Booking Bear: A Flexible Solution</strong></h2><p><strong>Flexible Stay‑Rule Settings</strong><br> Adjust rules by date range, day of week, or last‑minute windows with no tech headache.</p><p><strong>Beginner‑Friendly Setup</strong><br> Straightforward dashboards make changes accessible even if you’re not tech-savvy.</p><p><strong>Free tier for smaller hosts</strong><br> Try different strategies risk‑free, track performance, and see what drives revenue before committing.</p><h2><strong>FAQs</strong></h2><p><strong>Q1: Should I ever use a flat minimum night rule?</strong><br> Sure—if your property is small and weekends are always booked, a flat 2–3 night rule may reduce admin—but test flexibility in slower times.</p><p><strong>Q2: How do minimum nights affect cleaning costs?</strong><br> Fewer turnovers = lower weekly cleaning costs and less wear‐and‐tear. But fewer bookings overall can negate the saving, so balance is key.</p><p><strong>Q3: Can short‑stay guests become long‑stay?</strong><br> Absolutely. Many one‑night guests return for vacations or refer friends. Don’t write them off.</p><p><strong>Q4: What’s the risk with no minimum nights?</strong><br> More cleaning, faster turnover, higher admin time—but more bookings, potentially higher overall income.</p><p><strong>Q5: How to test different policies with Booking Bear?</strong><br> Use date‑based rules, setup last‑minute overrides, and track occupancy/earnings to compare. Change easy, reset easy.</p><p><strong>Q6: Will OTAs penalise me for setting short‑stay options?</strong><br> Actually, OTAs often prefer allowing 1-night stays—they match more searches and may favour you in listings.</p><h2><strong>Conclusion</strong></h2><p>There’s no one‑size‑fits‑all solution. Minimum night rules <em>can</em> help by reducing turnover, raising per‑booking income, and easing workload—<strong>but they can also close doors on valuable short‑stay guests</strong>. By using a flexible approach—weekend rules, off‑peak openings, last‑minute availability—you get the best of both worlds.</p><p>Booking Bear makes it easy to experiment without coding skills or confusion. <a href=\"/register\"><strong>Try Booking Bear free tier, or&nbsp; for 2 months!</strong>&nbsp;</a>See what works—and iterate your stay rules for growth and freedom. Happy hosting!</p><p><br></p>', NULL, 1, '2025-06-17 11:39:52', '2025-06-17 09:47:45', '2025-06-17 09:47:45'),
(4, 1, 'Boost Your Accommodation Business with Our New Analytics Dashboard', 'boost-your-accommodation-business-with-our-new-analytics-dashboard', 'We\'ve just launched a brand new analytics dashboard.', '<h2>See Your Business Clearly</h2><p>Running an accommodation business means juggling a lot of information. How many people are searching for your properties? Which ones are most popular? When should you adjust your prices?</p><p>Our new Analytics Dashboard answers these questions at a glance. We\'ve designed it to give you clear, actionable insights without the complexity.</p><h2>What Can You Do With It?</h2><h3>Understand Your Popularity</h3><p>See which of your properties attract the most interest. Our dashboard ranks your accommodations by search volume, showing you exactly where guests are looking.</p><h3>Track Your Success Rate</h3><p>Converting searches to bookings is what matters. The dashboard shows your:</p><ul><li>Total number of searches</li><li>Conversion rate (how many available searches become bookings)</li><li>Availability rate (how often your properties can be booked)</li><li>Confirmed booking count</li></ul><h3>Spot Seasonal Patterns</h3><p>Notice when demand peaks throughout the year. The dashboard highlights:</p><ul><li>Monthly trends</li><li>Day-of-week preferences (weekend vs. weekday)</li><li>Most requested stay durations</li></ul><p>This makes planning your pricing strategy much easier.</p><h2>Smart Insights, Not Just Data</h2><p>The dashboard doesn\'t just show numbers—it tells you what they mean:</p><blockquote>\"Your conversion rate is excellent! Consider increasing prices slightly to maximize revenue.\"\"Search volume is trending upward. Great time to optimize pricing.\"</blockquote><p>These automated recommendations help you take immediate action to improve your business.</p><h2>Easy to Use, Powerful Results</h2><p>You don\'t need to be a data expert. Simply:</p><ol><li>Select your date range</li><li>Choose which properties to analyze</li><li>Review your metrics and insights</li><li>Export reports if needed</li></ol><p>The clean, mobile-friendly design works wherever you are—on your desktop, tablet, or phone.</p><h2>Make Better Business Decisions</h2><p>Stop guessing about your business performance. With our Analytics Dashboard, you\'ll know:</p><ul><li>When to raise or lower prices</li><li>Which properties need better marketing</li><li>How to optimize your calendar availability</li><li>Where you\'re missing booking opportunities</li></ul><h2>Ready to Boost Your Business?</h2><p><a href=\"/login\"><span style=\"text-decoration: underline;\">Log in</span></a> or <a href=\"/register\">s<span style=\"text-decoration: underline;\">ign up</span></a> today and click on \"Analytics\" in your dashboard. The insights you need are waiting for you.</p><p>Your accommodation business generates valuable data every day. Our Analytics Dashboard turns that data into your competitive advantage.</p><p><br></p>', NULL, 1, '2025-06-19 15:28:12', '2025-06-19 13:39:19', '2025-06-19 13:40:36'),
(5, 1, 'How to Drive Direct Bookings with Google Maps & Local SEO (No Listings Needed)', 'how-to-drive-direct-bookings', 'Tired of losing guests to big booking platforms? Learn how small stay owners in South Africa can use Google Maps and local SEO to drive direct bookings—no tech skills needed.', '<h2>Tired of Paying Commissions? Google Maps Can Help</h2><p>If you\'re running a guesthouse, B&amp;B, campsite, or self-catering stay, chances are you’ve felt the sting of commission fees from online booking sites like Airbnb or LekkeSlaap. Worse still, potential guests might be clicking on your competitors just because you don\'t show up when they search “places to stay near [your area]” on Google.</p><p>But here’s the good news: by using <em>Google Maps</em> and <em>local SEO for accommodation</em>, you can bring guests straight to your own website. No commission. No middleman. Just direct bookings and more control.</p><p>This guide will show you—step by step—how to get started. You don’t need to be a tech whiz. Just follow along, and you’ll be visible to more local travelers in no time.</p><h2>Why Google Maps &amp; Local SEO Matter for Small Stays</h2><ul><li>Over <strong>60% of travelers</strong> use Google to plan and book accommodation.</li><li>Most start with searches like “B&amp;B near Stellenbosch” or “guesthouse in Langebaan”.</li><li>If your place doesn’t appear on Google Maps or in the local results, you’re invisible.</li></ul><p><strong>Local search</strong> helps you show up when people are actively planning their trips—often just days before check-in. Being there at the right time means more <em>direct bookings with Google Maps</em>, fewer empty rooms, and no commissions.</p><h2><strong>Step 1: Set Up (or Claim) Your Google Business Profile</strong></h2><p>Your <strong>Google Business Profile</strong> is what shows up in Maps and on the right-hand side of Google results. It’s <em>completely free</em>—and essential.</p><h3><strong>How to check if you already have one:</strong></h3><ul><li>Go to <a href=\"https://business.google.com\"><span style=\"text-decoration: underline;\">https://business.google.com</span></a></li><li>Sign in with your Gmail account.</li><li>Search for your business name and location.</li></ul><p>If it’s already listed but unclaimed, follow the prompts to claim it.</p><h3><strong>Set it up the right way:</strong></h3><ul><li><strong>Business name:</strong> Use your real guesthouse name (e.g. “Sunset Haven Guesthouse”)</li><li><strong>Category:</strong> Choose the closest match — “Guesthouse”, “Bed &amp; Breakfast”, “Self-Catering Accommodation”</li><li><strong>Phone number &amp; website:</strong> Use your direct line and your <em>own</em> site</li><li><strong>Description:</strong> Describe your stay — mention key attractions nearby</li><li><strong>Photos:</strong> Upload:<ul><li>2–3 exterior shots</li><li>3–5 interior shots (bedrooms, bathrooms, kitchen if applicable)</li><li>View or garden if you have one</li></ul></li></ul><p><strong>Pro tip:</strong> Listings with 10+ photos get far more clicks.</p><h2><strong>Step 2: Get Reviews &amp; Keep Them Fresh</strong></h2><p>Google ranks places higher when they have regular, positive reviews.</p><h3><strong>Ask every happy guest:</strong></h3><ul><li>In your thank-you email or message: “Would you mind leaving us a quick Google review? It really helps small stays like ours.”</li></ul><h3><strong>Respond to every review—good or bad:</strong></h3><ul><li>“Thanks so much, hope to host you again!”</li><li>“Sorry your water pressure was low—thanks for the feedback, we’ve fixed it!”</li></ul><p>Even with just <strong>5–10 good reviews</strong>, you can climb the local search rankings and start attracting more guests.</p><h2><strong>Step 3: Add a Booking Link to YOUR Website</strong></h2><p>Many hosts make the mistake of linking to their <strong>Airbnb</strong> or <strong>LekkeSlaap</strong> page from Google. That means guests:</p><ul><li>See competitor listings</li><li>Might book elsewhere</li><li>And you still pay commission</li></ul><h3><strong>Fix it in 2 minutes:</strong></h3><ul><li>Go to your Google Business dashboard</li><li>Click “Edit profile”</li><li>Under <strong>Website</strong>, paste the <strong>link to your own booking page</strong></li></ul><p>If you’re using <strong>Booking Bear</strong> or a similar tool, make sure it’s the <strong>booking page</strong>, not just the homepage.</p><h2><strong>Step 4: Local SEO Basics for Your Website</strong></h2><p>You don’t need a fancy website to show up in local search—just the <em>right words in the right places</em>.</p><h3><strong>Use keywords guests search for:</strong></h3><ul><li>On your homepage: “A peaceful guesthouse in Montagu near the wine route.”</li><li>On your “About” page: “We’re a family-run B&amp;B located in the heart of the Garden Route.”</li></ul><h3><strong>Mention nearby attractions:</strong></h3><ul><li>Beaches, wine farms, mountain hikes, parks, game drives</li><li>This helps you show up when guests search things like:<ul><li>“accommodation near Cederberg hiking trails”</li><li>“places to stay close to Franschhoek wine farms”</li></ul></li></ul><h3><strong>Extra (for the confident):</strong></h3><p>Use <strong>schema markup</strong> or structured data to label your business info—this can improve how your listing appears on search. You can ask your web person or check out Google’s Structured Data Tool.</p><h2><strong>Bonus Tips: Stay Visible, Stay Booked</strong></h2><ul><li><strong>Embed a Google Map</strong> on your website’s contact page</li><li>Update your profile with <strong>seasonal photos</strong> (e.g. spring flowers, winter fireplaces)</li><li>Add your stay to <strong>local directories</strong>:<ul><li><a href=\"https://www.sa-venues.com\"><span style=\"text-decoration: underline;\">SA-Venues</span></a></li><li><a href=\"https://www.travelstart.co.za\"><span style=\"text-decoration: underline;\">Travelstart</span></a></li><li><a href=\"https://www.sleeping-out.co.za\"><span style=\"text-decoration: underline;\">Sleeping-OUT</span></a></li></ul></li></ul><p>Every link back to your site helps build visibility.</p><h2><strong>Wrapping Up: You Can Do This (No Tech Skills Needed)</strong></h2><p>You don’t need to be a developer—or spend thousands on ads—to <em>increase direct bookings B&amp;B</em> or guesthouse.</p><p>With a little time and consistency, using <em>Google Maps and local SEO for accommodation</em> puts you in front of the guests who are already looking.</p><p>Just start with your <strong>Google Business Profile</strong>, add reviews, and make sure your site has the right keywords. The rest builds from there.</p><p>Want a simple, commission-free way to take bookings directly? <a href=\"/register\"><strong>Try Booking Bear free</strong></a></p><h2><strong>FAQs: Direct Bookings with Google Maps</strong></h2><h3><strong>1. Is Google Business Profile really free?</strong></h3><p>Yes, totally free. You don’t pay Google anything to be listed or get reviews.</p><h3><strong>2. What if I don’t have a website yet?</strong></h3><p>You can still get listed, but it’s best to build a simple site (even one page) with booking details. Tools like Wix or Booking Bear help you set up fast.</p><h3><strong>3. How long until I show up in search results?</strong></h3><p>You can start appearing within a few days, especially if your info is complete and you have a few reviews.</p><h3><strong>4. Can I remove bad reviews?</strong></h3><p>No, but you can respond to them professionally. That shows guests you care.</p><h3><strong>5. Do I need to pay someone to do local SEO?</strong></h3><p>Not at all. This guide is everything you need to get started—and it works.</p><h3><strong>6. Will this work even if I’m in a small town?</strong></h3><p>Absolutely. In fact, fewer competitors in small towns often means faster results.</p><p><br></p>', NULL, 1, '2025-06-24 14:57:19', '2025-06-24 13:20:20', '2025-06-24 13:20:20'),
(6, 1, 'Self-Hosted vs Marketplace Listings: Which One Wins for Local Hosts?', 'self-hosted-vs-marketplace-listings', 'Torn between Airbnb and building your own booking site? Discover the pros and cons of self-hosted booking systems vs marketplace listings—and how to take control of your guest experience.', '<h2><strong>1. Introduction</strong></h2><p>If you run a small guesthouse, B&amp;B, or campsite in South Africa—especially in the Western Cape—chances are you’ve listed on Airbnb, Booking.com, or LekkeSlaap. And why not? These platforms offer instant exposure, make bookings easy, and come with built-in trust.</p><p>But over time, many hosts begin to feel the sting: high commissions, limited control, and guests who remember the platform—not your property. If that sounds familiar, you’re not alone. A growing number of local hosts are asking: <strong>Is there a better way?</strong></p><p>In this post, we’ll break down the pros and cons of using a <strong>self-hosted booking system vs marketplace</strong>, so you can decide what’s best for your property—and your future.</p><h2><strong>2. What Do We Mean by “Self-Hosted” vs “Marketplace”?</strong></h2><p>Let’s clear up the terms first:</p><ul><li><strong>Self-hosted booking system</strong>: This means running your own website with a booking engine embedded. Guests book directly with you, often without middlemen. You control everything—from your cancellation policy to how your rooms are displayed.</li><li><strong>Marketplace listing</strong>: Platforms like Airbnb, Booking.com, and LekkeSlaap fall into this category. You list your property on their site, and they manage the bookings, payments, and guest communication.</li></ul><p>Most small accommodation owners use both—but usually, <strong>one ends up driving most of the bookings</strong>.</p><h2><strong>3. The Case for Marketplace Listings</strong></h2><h3>✅ Instant Exposure to Millions of Guests</h3><p>Marketplace platforms bring global visibility without the effort of building your own audience. Your listing appears next to others, and people searching for accommodation in your area can find you immediately.</p><h3>✅ Built-in Trust and Reviews</h3><p>Guests often trust platforms like Airbnb because of their review systems, security, and refund policies. It’s easier to convince a first-time visitor to book through these familiar channels.</p><h3>✅ Secure Payment Handling</h3><p>No need to worry about handling credit card info or setting up merchant accounts—these platforms handle the transaction and often offer fraud protection.</p><h3>❌ High Fees (10–20% or More)</h3><p>You lose a chunk of every booking. And it adds up. Not only do platforms charge you, but some also charge the guest—making your price less competitive.</p><h3>❌ Guest is Loyal to the Platform, Not You</h3><p>Most guests remember “Airbnb” or “LekkeSlaap,” not your brand name. That makes it harder to build repeat bookings or word-of-mouth recognition.</p><h3>❌ You’re Just One Listing Among Thousands</h3><p>Unless you’re priced incredibly well or have standout photos, you can easily get buried in the competition—especially in high-tourism areas.</p><h2><strong>4. The Case for Self-Hosting</strong></h2><h3>✅ No Commission Fees = More Revenue per Booking</h3><p>Direct bookings mean <strong>100% of the payment goes to you</strong>. Over time, this makes a big financial difference—especially during peak seasons.</p><h3>✅ Guests Interact with Your Brand</h3><p>Your own website puts your guesthouse or lodge front and center. You can tell your story, show your personality, and build a brand that stands out.</p><h3>✅ You Control the Experience</h3><p>You decide the cancellation policy. You choose which photos to show. You write the descriptions and answer the inquiries—on your terms.</p><h3>✅ Great for Building Repeat Business</h3><p>Once a guest has booked with you directly, you can offer specials, stay in touch, and turn them into a loyal fan who bypasses platforms next time.</p><h3>❌ Takes Effort to Set Up</h3><p>Creating a website, choosing a booking system, and making it look professional takes time and money—especially if you’re not tech-savvy.</p><h3>❌ You’re Responsible for Traffic &amp; Trust-Building</h3><p>You need to get people to your site, which can involve learning some basic online marketing or relying on tools that help boost visibility.</p><h2><strong>5. What Works Best for Local Hosts?</strong></h2><p>The smartest route for most small accommodation owners? <strong>Use both</strong>.</p><p>Marketplaces are fantastic for bringing in first-time guests. But once they’ve stayed with you, give them a reason to <strong>book direct next time</strong>—by offering a discount, better room options, or a more personal experience.</p><p>A <strong>self-hosted booking system</strong> works especially well if:</p><ul><li>You have guests who return year after year</li><li>You run a unique or niche property that appeals to a specific group</li><li>You want to reduce your long-term dependency on third-party platforms</li></ul><p>By combining your own site with strong local SEO, visibility on Google Maps, and tools like Booking Bear, you can attract guests and convert them into direct bookings.</p><h2><strong>6. How Booking Bear Supports Self-Hosting</strong></h2><p><strong>Booking Bear</strong> makes it incredibly easy to take your first step into self-hosting. Here’s how it helps:</p><ul><li><strong>Embed a booking widget in minutes</strong> on your own website—even if you built it yourself</li><li><strong>No tech skills required</strong>: It’s designed for non-technical users, with simple setup and support</li><li><strong>You stay in control</strong>: Manage your calendar, set your own rules, and offer your unique value</li><li><strong>Perfect for niche properties</strong>: From mountain cabins to beachside campsites, Booking Bear works where big platforms often fall short</li></ul><p>Instead of replacing your marketplace listings overnight, Booking Bear lets you <strong>build independence at your own pace</strong>.</p><h2><strong>7. Conclusion</strong></h2><p>There’s no one-size-fits-all solution. But if you’re feeling frustrated by fees, lack of control, or being just another listing—<strong>it might be time to own your bookings</strong>.</p><p>Marketplace platforms are useful, especially for exposure. But building your own online presence is an investment in your brand, your income, and your future.</p><p><a href=\"/register\"><strong>Try Booking Bear for free</strong></a> and take that first step toward more control, better margins, and a direct connection with your guests.</p><h2><strong>Frequently Asked Questions</strong></h2><h3><strong>1. Can I use both a self-hosted booking system and Airbnb at the same time?</strong></h3><p>Yes! In fact, many small hosts do. Use Airbnb to get first-time bookings and your own site to encourage repeat ones.</p><h3><strong>2. Is it expensive to build my own booking website?</strong></h3><p>Not necessarily. Tools like Booking Bear let you embed a booking engine into an existing site—so you don’t have to start from scratch or hire developers.</p><h3><strong>3. What are the risks of relying only on marketplaces?</strong></h3><p>You may face rising fees, platform rule changes, or even sudden account suspensions. Plus, you don’t own your guest data or brand identity.</p><h3><strong>4. How do I drive traffic to my own website?</strong></h3><p>Start with local SEO (like optimizing your Google Maps listing), share your link in emails and social media, and ask past guests to book direct next time.</p><h3><strong>5. Do guests trust direct booking sites?</strong></h3><p>Yes—if your site looks professional and includes secure payment options. Using a trusted tool like Booking Bear helps establish that credibility.</p><h3><strong>6. How long does it take to get set up with a self-hosted booking system?</strong></h3><p>You can start in less than a day if you\'re using user-friendly tools. Over time, you can expand and improve your site as needed.</p><p><br></p>', NULL, 1, '2025-06-30 08:40:30', '2025-06-30 08:44:29', '2025-06-30 08:44:29');

-- --------------------------------------------------------

--
-- Table structure for table `blog_category`
--

CREATE TABLE `blog_category` (
  `blog_id` bigint UNSIGNED NOT NULL,
  `category_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blog_category`
--

INSERT INTO `blog_category` (`blog_id`, `category_id`, `created_at`, `updated_at`) VALUES
(1, 2, '2025-06-09 17:28:50', '2025-06-09 17:28:50'),
(1, 3, '2025-06-09 17:28:50', '2025-06-09 17:28:50'),
(2, 1, '2025-06-09 17:46:53', '2025-06-09 17:46:53'),
(2, 3, '2025-06-09 17:46:53', '2025-06-09 17:46:53'),
(3, 1, '2025-06-17 09:47:45', '2025-06-17 09:47:45'),
(3, 2, '2025-06-17 09:47:45', '2025-06-17 09:47:45'),
(4, 5, '2025-06-19 13:39:19', '2025-06-19 13:39:19'),
(5, 3, '2025-06-24 13:20:20', '2025-06-24 13:20:20'),
(6, 1, '2025-06-30 08:44:29', '2025-06-30 08:44:29');

-- --------------------------------------------------------

--
-- Table structure for table `bookings`
--

CREATE TABLE `bookings` (
  `id` bigint UNSIGNED NOT NULL,
  `accommodation_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `contact_number` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `first_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `occupancy` int UNSIGNED NOT NULL DEFAULT '1',
  `total_price` decimal(10,2) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `booking_status_id` bigint UNSIGNED DEFAULT NULL,
  `notes` text COLLATE utf8mb4_unicode_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `bookings`
--

INSERT INTO `bookings` (`id`, `accommodation_id`, `user_id`, `email`, `contact_number`, `first_name`, `last_name`, `start_date`, `end_date`, `occupancy`, `total_price`, `created_at`, `updated_at`, `booking_status_id`, `notes`) VALUES
(1, 8, 5, '<EMAIL>', '0825133115', 'John', 'Doe', '2025-05-29', '2025-05-30', 2, 2500.00, '2025-05-27 09:46:01', '2025-05-27 09:46:01', 1, '2012 Sprite swing, wheelbearings'),
(2, 1, 1, '<EMAIL>', '0741875150', 'Riaan', 'Laubscher', '2025-07-01', '2025-07-03', 2, 800.00, '2025-06-06 09:50:09', '2025-06-06 09:50:09', 2, 'wewe'),
(3, 10, 1, '<EMAIL>', '0741875150', 'Riaan', 'Laubscher', '2025-07-28', '2025-07-31', 2, 3600.00, '2025-06-12 11:38:09', '2025-06-18 14:12:35', 1, 'test');

-- --------------------------------------------------------

--
-- Table structure for table `booking_statuses`
--

CREATE TABLE `booking_statuses` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `color` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#000000',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `booking_statuses`
--

INSERT INTO `booking_statuses` (`id`, `name`, `description`, `color`, `created_at`, `updated_at`) VALUES
(1, 'Confirmed', 'Booking has been confirmed', '#10b981', '2025-05-23 08:52:22', '2025-05-23 08:52:22'),
(2, 'Pending', 'Booking is pending confirmation', '#f59e0b', '2025-05-23 08:52:22', '2025-05-23 08:52:22'),
(3, 'Cancelled', 'Booking has been cancelled', '#ef4444', '2025-05-23 08:52:22', '2025-05-23 08:52:22'),
(4, 'Completed', 'Booking has been completed', '#3b82f6', '2025-05-23 08:52:22', '2025-05-23 08:52:22'),
(5, 'No-Show', 'Guest did not show up', '#8b5cf6', '2025-05-23 08:52:22', '2025-05-23 08:52:22');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cache`
--

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('booking_bear_cache_0d45cedbcdeaa3bf5d141213e73b5ebc', 'i:1;', 1749708472),
('booking_bear_cache_0d45cedbcdeaa3bf5d141213e73b5ebc:timer', 'i:1749708472;', 1749708472),
('booking_bear_cache_356a192b7913b04c54574d18c28d46e6395428ab', 'i:1;', 1751265929),
('booking_bear_cache_356a192b7913b04c54574d18c28d46e6395428ab:timer', 'i:1751265929;', 1751265929),
('booking_bear_cache_97ddde8ad8c00d8590b2c03b3b23324b', 'i:1;', 1750766054),
('booking_bear_cache_97ddde8ad8c00d8590b2c03b3b23324b:timer', 'i:1750766054;', 1750766054),
('booking_bear_cache_analytics_conversion_1_2025-05-19_2025-06-18_d3e15381bba6090e53908fe666ec2415', 'a:5:{s:14:\"total_searches\";i:11;s:18:\"available_searches\";i:7;s:18:\"confirmed_bookings\";i:1;s:15:\"conversion_rate\";d:14.29;s:17:\"availability_rate\";d:63.64;}', 1750257842),
('booking_bear_cache_analytics_date_analysis_1_2025-05-19_2025-06-18_d3e15381bba6090e53908fe666ec2415', 'a:3:{s:7:\"monthly\";a:2:{i:0;a:5:{s:6:\"period\";s:8:\"Jun 2025\";s:5:\"month\";i:6;s:4:\"year\";i:2025;s:12:\"search_count\";i:7;s:9:\"avg_price\";d:2412.5;}i:1;a:5:{s:6:\"period\";s:8:\"Jul 2025\";s:5:\"month\";i:7;s:4:\"year\";i:2025;s:12:\"search_count\";i:4;s:9:\"avg_price\";d:2633.33;}}s:11:\"day_of_week\";a:4:{i:0;a:3:{s:3:\"day\";s:6:\"Monday\";s:12:\"search_count\";i:3;s:9:\"avg_price\";d:2750;}i:1;a:3:{s:3:\"day\";s:7:\"Tuesday\";s:12:\"search_count\";i:1;s:9:\"avg_price\";d:2400;}i:2;a:3:{s:3:\"day\";s:8:\"Thursday\";s:12:\"search_count\";i:4;s:9:\"avg_price\";d:2850;}i:3;a:3:{s:3:\"day\";s:8:\"Saturday\";s:12:\"search_count\";i:3;s:9:\"avg_price\";d:2266.67;}}s:13:\"stay_duration\";a:3:{i:0;a:3:{s:8:\"duration\";i:1;s:12:\"search_count\";i:3;s:9:\"avg_price\";d:1200;}i:1;a:3:{s:8:\"duration\";i:2;s:12:\"search_count\";i:6;s:9:\"avg_price\";d:2475;}i:2;a:3:{s:8:\"duration\";i:3;s:12:\"search_count\";i:2;s:9:\"avg_price\";d:3225;}}}', 1750263242),
('booking_bear_cache_analytics_popularity_1_2025-05-19_2025-06-18_d3e15381bba6090e53908fe666ec2415', 'a:3:{i:0;a:7:{s:16:\"accommodation_id\";i:9;s:18:\"accommodation_name\";s:22:\"Honeydrop Hollow Cabin\";s:12:\"search_count\";i:6;s:18:\"available_searches\";i:4;s:20:\"unavailable_searches\";i:2;s:17:\"availability_rate\";d:66.67;s:16:\"avg_quoted_price\";d:2300;}i:1;a:7:{s:16:\"accommodation_id\";i:13;s:18:\"accommodation_name\";s:17:\"Bearfoot Bungalow\";s:12:\"search_count\";i:4;s:18:\"available_searches\";i:2;s:20:\"unavailable_searches\";i:2;s:17:\"availability_rate\";d:50;s:16:\"avg_quoted_price\";d:2375;}i:2;a:7:{s:16:\"accommodation_id\";i:10;s:18:\"accommodation_name\";s:20:\"The Sleepy Den Lodge\";s:12:\"search_count\";i:1;s:18:\"available_searches\";i:1;s:20:\"unavailable_searches\";i:0;s:17:\"availability_rate\";d:100;s:16:\"avg_quoted_price\";d:3600;}}', 1750259642),
('booking_bear_cache_analytics_trends_1_2025-05-19_2025-06-18_d3e15381bba6090e53908fe666ec2415_552cbb9d6515dadbbc4718ad75114f08', 'a:3:{i:0;a:5:{s:6:\"period\";s:10:\"2025-06-12\";s:12:\"search_count\";i:8;s:15:\"available_count\";i:4;s:17:\"availability_rate\";d:50;s:9:\"avg_price\";d:2775;}i:1;a:5:{s:6:\"period\";s:10:\"2025-06-17\";s:12:\"search_count\";i:2;s:15:\"available_count\";i:2;s:17:\"availability_rate\";d:100;s:9:\"avg_price\";d:2025;}i:2;a:5:{s:6:\"period\";s:10:\"2025-06-18\";s:12:\"search_count\";i:1;s:15:\"available_count\";i:1;s:17:\"availability_rate\";d:100;s:9:\"avg_price\";d:2400;}}', 1750256942),
('booking_bear_cache_analytics_unavailability_1_2025-05-19_2025-06-18_d3e15381bba6090e53908fe666ec2415', 'a:2:{i:0;a:3:{s:6:\"reason\";s:14:\"minimum_notice\";s:5:\"count\";i:3;s:10:\"percentage\";d:75;}i:1;a:3:{s:6:\"reason\";s:12:\"minimum_stay\";s:5:\"count\";i:1;s:10:\"percentage\";d:25;}}', 1750257842),
('booking_bear_cache_fe5dbbcea5ce7e2988b8c69bcfdfde8904aabc1f', 'i:1;', 1749649979),
('booking_bear_cache_fe5dbbcea5ce7e2988b8c69bcfdfde8904aabc1f:timer', 'i:1749649979;', 1749649979),
('booking_bear_cache_livewire-rate-limiter:9a4ea46c6e37b4824ef807553ad20546b7a6d561', 'i:1;', 1750769296),
('booking_bear_cache_livewire-rate-limiter:9a4ea46c6e37b4824ef807553ad20546b7a6d561:timer', 'i:1750769296;', 1750769296),
('booking_bear_cache_plan_change_notification_1_sub_01jxf7wsz7agz0tjztkzkxn6bx', 'b:1;', 1749641650);

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `owner` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `expiration` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cashier_subscriptions`
--

CREATE TABLE `cashier_subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `billable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint UNSIGNED NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `paddle_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `paused_at` timestamp NULL DEFAULT NULL,
  `ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cashier_subscription_items`
--

CREATE TABLE `cashier_subscription_items` (
  `id` bigint UNSIGNED NOT NULL,
  `cashier_subscription_id` bigint UNSIGNED NOT NULL,
  `product_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `price_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `quantity` int NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cashier_transactions`
--

CREATE TABLE `cashier_transactions` (
  `id` bigint UNSIGNED NOT NULL,
  `billable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint UNSIGNED NOT NULL,
  `paddle_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `paddle_subscription_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `invoice_number` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `tax` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `billed_at` timestamp NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `cashier_transactions`
--

INSERT INTO `cashier_transactions` (`id`, `billable_type`, `billable_id`, `paddle_id`, `paddle_subscription_id`, `invoice_number`, `status`, `total`, `tax`, `currency`, `billed_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\User', 1, 'txn_01jwxpcex9jsxanrrwnawkd4tp', 'sub_01jtdw0nm2gznfgw55cmprpqaa', '13217-10055', 'completed', '24900', '3248', 'ZAR', '2025-06-04 14:44:05', '2025-06-04 14:44:10', '2025-06-04 14:44:10'),
(2, 'App\\Models\\User', 1, 'txn_01jwxpv4s6krs2vpgwh1hzt5ea', 'sub_01jtdwg2m1hzkmw4endkwwpa3t', '13217-10056', 'completed', '24900', '3248', 'ZAR', '2025-06-04 14:52:06', '2025-06-04 14:52:12', '2025-06-04 14:52:12'),
(3, 'App\\Models\\User', 1, 'txn_01jwxs65z2nk12zj4wgmvtx6g4', 'sub_01jtdytrmkjfrvgj84g5f350d7', '13217-10057', 'completed', '24900', '3248', 'ZAR', '2025-06-04 15:33:05', '2025-06-04 15:33:11', '2025-06-04 15:33:11'),
(4, 'App\\Models\\User', 1, 'txn_01jwxstd1erthp3mssxmzj0q7j', 'sub_01jtdzfdec9vm96pzvnxp4zdvy', '13217-10058', 'completed', '24900', '3248', 'ZAR', '2025-06-04 15:44:07', '2025-06-04 15:44:13', '2025-06-04 15:44:13'),
(5, 'App\\Models\\User', 1, 'txn_01jwxt770phnmq5m7kq19n7xy7', 'sub_01jtdzw5nszkc00yga2cgnt937', '13217-10059', 'completed', '24900', '3248', 'ZAR', '2025-06-04 15:51:07', '2025-06-04 15:51:13', '2025-06-04 15:51:13'),
(6, 'App\\Models\\User', 1, 'txn_01jwxtckfy1tp0x7ywypyyhvnw', 'sub_01jte014cg3mmyc8enyqsnbcyg', '13217-10060', 'completed', '24900', '3248', 'ZAR', '2025-06-04 15:54:04', '2025-06-04 15:54:09', '2025-06-04 15:54:09'),
(7, 'App\\Models\\User', 1, 'txn_01jwzcesq83nxq886x3m4xw7v4', 'sub_01jtfj42s04pfj4gs9yv43gdhh', '13217-10061', 'completed', '24900', '3248', 'ZAR', '2025-06-05 06:29:04', '2025-06-05 06:29:10', '2025-06-05 06:29:10'),
(8, 'App\\Models\\User', 1, 'txn_01jx07mhxj87txp6nrsd7fvxh2', 'sub_01jtgd8rtrm738rtmed10vncr1', '13217-10062', 'completed', '24900', '3248', 'ZAR', '2025-06-05 14:24:05', '2025-06-05 14:24:10', '2025-06-05 14:24:10');

-- --------------------------------------------------------

--
-- Table structure for table `categories`
--

CREATE TABLE `categories` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `categories`
--

INSERT INTO `categories` (`id`, `name`, `slug`, `created_at`, `updated_at`) VALUES
(1, 'Website & Booking System Tips', 'website-booking-system-tips', '2025-06-09 17:27:28', '2025-06-09 17:27:28'),
(2, 'Pricing & Revenue', 'pricing-revenue', '2025-06-09 17:27:44', '2025-06-09 17:27:44'),
(3, 'Growth, Marketing & Guest Experience', 'growth-marketing-guest-experience', '2025-06-09 17:28:01', '2025-06-09 17:28:01'),
(4, 'Community', 'community', '2025-06-09 17:28:23', '2025-06-09 17:28:23'),
(5, 'News', 'news', '2025-06-19 13:27:00', '2025-06-19 13:27:00');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` bigint UNSIGNED NOT NULL,
  `billable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `billable_id` bigint UNSIGNED NOT NULL,
  `paddle_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `trial_ends_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `billable_type`, `billable_id`, `paddle_id`, `name`, `email`, `trial_ends_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\User', 2, 'ctm_01jv9tr7q69s27tvkv6se8z2cn', 'Riaan Laubscher', '<EMAIL>', NULL, '2025-05-23 08:55:02', '2025-05-23 08:55:02'),
(2, 'App\\Models\\User', 4, 'ctm_01jvyn6drettrnf4xkfejhhtct', 'Riaan Laubscher', '<EMAIL>', NULL, '2025-05-23 13:26:51', '2025-05-23 13:26:51'),
(3, 'App\\Models\\User', 5, 'ctm_01jw8h877dhtfcsfsx4dy6rf6n', 'Riaan', '<EMAIL>', NULL, '2025-05-27 09:30:20', '2025-05-27 09:30:20'),
(4, 'App\\Models\\User', 1, 'ctm_01jscr0fdt0ktr6bcgdhgjn8cb', 'Riaan Laubscher', '<EMAIL>', NULL, '2025-05-30 07:23:21', '2025-05-30 07:23:21'),
(5, 'App\\Models\\User', 8, 'ctm_01jxfm63qh1kyenv43yd2y9qf1', 'Riaan Laubscher', '<EMAIL>', NULL, '2025-06-11 13:51:59', '2025-06-11 13:51:59');

-- --------------------------------------------------------

--
-- Table structure for table `exports`
--

CREATE TABLE `exports` (
  `id` bigint UNSIGNED NOT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `file_disk` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `exporter` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processed_rows` int UNSIGNED NOT NULL DEFAULT '0',
  `total_rows` int UNSIGNED NOT NULL,
  `successful_rows` int UNSIGNED NOT NULL DEFAULT '0',
  `user_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_import_rows`
--

CREATE TABLE `failed_import_rows` (
  `id` bigint UNSIGNED NOT NULL,
  `data` json NOT NULL,
  `import_id` bigint UNSIGNED NOT NULL,
  `validation_error` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `uuid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `connection` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `queue` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `exception` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `failed_jobs`
--

INSERT INTO `failed_jobs` (`id`, `uuid`, `connection`, `queue`, `payload`, `exception`, `failed_at`) VALUES
(10, 'a3b5703e-50e1-4d61-b966-61151a307f08', 'database', 'default', '{\"uuid\":\"a3b5703e-50e1-4d61-b966-61151a307f08\",\"displayName\":\"App\\\\Jobs\\\\AddUserToBrevoContact\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Jobs\\\\AddUserToBrevoContact\",\"command\":\"O:30:\\\"App\\\\Jobs\\\\AddUserToBrevoContact\\\":1:{s:7:\\\"\\u0000*\\u0000user\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:6;s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}}\"}}', 'Error: Class \"Brevo\\Client\\Configuration\" not found in /home/<USER>/www.bookingbear.co.za/app/Services/BrevoContactService.php:20\nStack trace:\n#0 [internal function]: App\\Services\\BrevoContactService->__construct()\n#1 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Container.php(1062): ReflectionClass->newInstanceArgs()\n#2 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Container.php(890): Illuminate\\Container\\Container->build()\n#3 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1078): Illuminate\\Container\\Container->resolve()\n#4 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Container.php(821): Illuminate\\Foundation\\Application->resolve()\n#5 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1058): Illuminate\\Container\\Container->make()\n#6 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(124): Illuminate\\Foundation\\Application->make()\n#7 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/helpers.php(804): app()\n#8 /home/<USER>/www.bookingbear.co.za/app/Jobs/AddUserToBrevoContact.php(37): resolve()\n#9 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): App\\Jobs\\AddUserToBrevoContact->handle()\n#10 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()\n#11 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()\n#12 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#13 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#14 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(126): Illuminate\\Container\\Container->call()\n#15 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Bus\\Dispatcher->{closure:Illuminate\\Bus\\Dispatcher::dispatchNow():123}()\n#16 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()\n#17 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php(130): Illuminate\\Pipeline\\Pipeline->then()\n#18 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(126): Illuminate\\Bus\\Dispatcher->dispatchNow()\n#19 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Queue\\CallQueuedHandler->{closure:Illuminate\\Queue\\CallQueuedHandler::dispatchThroughMiddleware():121}()\n#20 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():168}()\n#21 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(121): Illuminate\\Pipeline\\Pipeline->then()\n#22 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/CallQueuedHandler.php(69): Illuminate\\Queue\\CallQueuedHandler->dispatchThroughMiddleware()\n#23 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Jobs/Job.php(102): Illuminate\\Queue\\CallQueuedHandler->call()\n#24 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(442): Illuminate\\Queue\\Jobs\\Job->fire()\n#25 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(392): Illuminate\\Queue\\Worker->process()\n#26 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Worker.php(178): Illuminate\\Queue\\Worker->runJob()\n#27 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(149): Illuminate\\Queue\\Worker->daemon()\n#28 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Queue/Console/WorkCommand.php(132): Illuminate\\Queue\\Console\\WorkCommand->runWorker()\n#29 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(36): Illuminate\\Queue\\Console\\WorkCommand->handle()\n#30 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()\n#31 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure()\n#32 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()\n#33 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Container/Container.php(754): Illuminate\\Container\\BoundMethod::call()\n#34 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Console/Command.php(213): Illuminate\\Container\\Container->call()\n#35 /home/<USER>/www.bookingbear.co.za/vendor/symfony/console/Command/Command.php(279): Illuminate\\Console\\Command->execute()\n#36 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Console/Command.php(182): Symfony\\Component\\Console\\Command\\Command->run()\n#37 /home/<USER>/www.bookingbear.co.za/vendor/symfony/console/Application.php(1094): Illuminate\\Console\\Command->run()\n#38 /home/<USER>/www.bookingbear.co.za/vendor/symfony/console/Application.php(342): Symfony\\Component\\Console\\Application->doRunCommand()\n#39 /home/<USER>/www.bookingbear.co.za/vendor/symfony/console/Application.php(193): Symfony\\Component\\Console\\Application->doRun()\n#40 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/Console/Kernel.php(198): Symfony\\Component\\Console\\Application->run()\n#41 /home/<USER>/www.bookingbear.co.za/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle()\n#42 /home/<USER>/www.bookingbear.co.za/artisan(16): Illuminate\\Foundation\\Application->handleCommand()\n#43 {main}', '2025-06-06 15:28:10');

-- --------------------------------------------------------

--
-- Table structure for table `features`
--

CREATE TABLE `features` (
  `id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `name` json NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` json DEFAULT NULL,
  `value` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `resettable_period` smallint UNSIGNED NOT NULL DEFAULT '0',
  `resettable_interval` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'month',
  `sort_order` mediumint UNSIGNED NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `features`
--

INSERT INTO `features` (`id`, `plan_id`, `name`, `slug`, `description`, `value`, `resettable_period`, `resettable_interval`, `sort_order`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, '{\"en\": \"Accommodations\"}', 'accommodations', '{\"en\": \"Number of accommodations allowed\"}', '5', 0, 'month', 1, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(2, 1, '{\"en\": \"Accommodation Groups\"}', 'accommodation-groups', '{\"en\": \"Number of accommodation groups allowed\"}', '1', 0, 'month', 2, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(3, 1, '{\"en\": \"Bookings\"}', 'bookings', '{\"en\": \"Number of bookings allowed per month\"}', '10', 1, 'month', 3, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(4, 1, '{\"en\": \"Embed Widgets\"}', 'embed-widgets', '{\"en\": \"The number of embed widgets you can create\"}', '1', 0, 'month', 4, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(5, 2, '{\"en\": \"Accommodations\"}', 'accommodations', '{\"en\": \"Number of accommodations allowed\"}', '20', 0, 'month', 5, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(6, 2, '{\"en\": \"Accommodation Groups\"}', 'accommodation-groups', '{\"en\": \"Number of accommodation groups allowed\"}', '5', 0, 'month', 6, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(7, 2, '{\"en\": \"Bookings\"}', 'bookings', '{\"en\": \"Number of bookings allowed per month\"}', '0', 1, 'month', 7, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(8, 2, '{\"en\": \"Embed Widgets\"}', 'embed-widgets', '{\"en\": \"The number of embed widgets you can create\"}', '-1', 0, 'month', 8, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `imports`
--

CREATE TABLE `imports` (
  `id` bigint UNSIGNED NOT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `importer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `processed_rows` int UNSIGNED NOT NULL DEFAULT '0',
  `total_rows` int UNSIGNED NOT NULL,
  `successful_rows` int UNSIGNED NOT NULL DEFAULT '0',
  `user_id` bigint UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint UNSIGNED NOT NULL,
  `queue` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `attempts` tinyint UNSIGNED NOT NULL,
  `reserved_at` int UNSIGNED DEFAULT NULL,
  `available_at` int UNSIGNED NOT NULL,
  `created_at` int UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_jobs` int NOT NULL,
  `pending_jobs` int NOT NULL,
  `failed_jobs` int NOT NULL,
  `failed_job_ids` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `options` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `cancelled_at` int DEFAULT NULL,
  `created_at` int NOT NULL,
  `finished_at` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `media`
--

CREATE TABLE `media` (
  `id` bigint UNSIGNED NOT NULL,
  `model_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `model_id` bigint UNSIGNED NOT NULL,
  `uuid` char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `collection_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `mime_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `disk` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `conversions_disk` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `size` bigint UNSIGNED NOT NULL,
  `manipulations` json NOT NULL,
  `custom_properties` json NOT NULL,
  `generated_conversions` json NOT NULL,
  `responsive_images` json NOT NULL,
  `order_column` int UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `media`
--

INSERT INTO `media` (`id`, `model_type`, `model_id`, `uuid`, `collection_name`, `name`, `file_name`, `mime_type`, `disk`, `conversions_disk`, `size`, `manipulations`, `custom_properties`, `generated_conversions`, `responsive_images`, `order_column`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\Accommodation', 6, '0be199b2-8142-4a7f-946a-d805bcbabbfe', 'gallery', 'sample1', 'sample1.jpeg', 'image/jpeg', 'public', 'public', 271367, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-05-23 13:29:02', '2025-05-23 13:29:03'),
(2, 'App\\Models\\AccommodationGroup', 2, '6fe66423-356c-4237-8fe7-2e625aa61cad', 'image', 'sample3', 'sample3.jpeg', 'image/jpeg', 'public', 'public', 479953, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-05-23 13:29:39', '2025-05-23 13:29:39'),
(3, 'App\\Models\\Blog', 1, 'fa10abff-6e7b-43db-a108-f0dc4d3645d5', 'feature_image', 'strategic-pricing-for-small-accommodation-operators', '01JWQYR6FB3QGM6XJM4J6Q9C13.jpg', 'image/jpeg', 'public', 'public', 152432, '[]', '[]', '[]', '[]', 1, '2025-06-02 09:14:51', '2025-06-02 09:14:51'),
(4, 'App\\Models\\Accommodation', 1, '610206df-7a50-4a3e-922b-03fca1be4d44', 'gallery', 'clouds_320_220_g', 'clouds_320_220_g.jpg', 'image/jpeg', 'public', 'public', 10612, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-06 08:03:34', '2025-06-06 08:03:35'),
(6, 'App\\Models\\Accommodation', 1, '5a834637-dee9-4413-a1e8-ae90d23c7791', 'gallery', 'trees_320_220_g', 'trees_320_220_g.jpg', 'image/jpeg', 'public', 'public', 25166, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 2, '2025-06-06 08:03:47', '2025-06-06 08:03:48'),
(7, 'App\\Models\\Blog', 2, '886b5bbe-80ee-44d0-921a-d08d779703d3', 'feature_image', '5-reasons-guests-leave-your-site-without-booking', '01JXAWTRQWNQV77FPS05JBJ9WA.webp', 'image/webp', 'public', 'public', 483364, '[]', '[]', '[]', '[]', 1, '2025-06-09 17:46:53', '2025-06-09 17:46:53'),
(8, 'App\\Models\\Accommodation', 9, 'afacdd07-6f59-4229-af02-be24f39f351d', 'gallery', 'honeydrop-hollow-cabin', 'honeydrop-hollow-cabin.webp', 'image/webp', 'public', 'public', 657130, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-12 08:59:01', '2025-06-12 08:59:02'),
(9, 'App\\Models\\Accommodation', 10, '20dc1d48-1d64-4ac9-893d-e59bf6391fda', 'gallery', 'sleepy-den-lodge', 'sleepy-den-lodge.webp', 'image/webp', 'public', 'public', 738378, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-12 08:59:33', '2025-06-12 08:59:34'),
(10, 'App\\Models\\Accommodation', 11, 'd782df2f-f2b8-438d-a2b2-4ef83074dbf6', 'gallery', 'pinecone-ridge-retreat', 'pinecone-ridge-retreat.webp', 'image/webp', 'public', 'public', 978858, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-12 08:59:48', '2025-06-12 08:59:49'),
(11, 'App\\Models\\Accommodation', 12, '81994d18-e65f-486f-8a1b-6dd80a5b96d1', 'gallery', 'the-sandy-paw', 'the-sandy-paw.webp', 'image/webp', 'public', 'public', 676668, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-12 09:00:03', '2025-06-12 09:00:04'),
(12, 'App\\Models\\Accommodation', 13, 'e7dd4bfa-e2bd-406d-9b06-1c656fedf1cb', 'gallery', 'bearfoot-bungelow', 'bearfoot-bungelow.webp', 'image/webp', 'public', 'public', 639418, '[]', '[]', '{\"large\": true, \"thumb\": true, \"medium\": true}', '[]', 1, '2025-06-12 09:00:18', '2025-06-12 09:00:18'),
(13, 'App\\Models\\Blog', 3, '869b1225-3e94-4a15-a2dd-3c7b3b810c4a', 'feature_image', 'minimum-night-stays', '01JXYMK7ERGEYY217RZ5X06A5G.webp', 'image/webp', 'public', 'public', 147306, '[]', '[]', '[]', '[]', 1, '2025-06-17 09:47:46', '2025-06-17 09:47:46'),
(14, 'App\\Models\\Blog', 4, '8f998848-e930-459e-a372-ec951449cf61', 'feature_image', 'analytics-dashboard', '01JY46MN6XH6T8S8TMM982T5KC.webp', 'image/webp', 'public', 'public', 94806, '[]', '[]', '[]', '[]', 1, '2025-06-19 13:39:19', '2025-06-19 13:39:19'),
(15, 'App\\Models\\Blog', 5, '5f1dcda3-83b2-42a7-9efa-de35c559ba39', 'feature_image', 'how-to-drive-direct-bookings', '01JYH1HG5DH1MQTPX0541NVFZV.webp', 'image/webp', 'public', 'public', 202668, '[]', '[]', '[]', '[]', 1, '2025-06-24 13:20:20', '2025-06-24 13:20:20'),
(16, 'App\\Models\\Blog', 6, '3fedcbd8-d7da-4973-9c5d-8421c1fa6ef0', 'feature_image', 'self-hosted-vs-marketplace-listings', '01JZ004PMKYJ57D4KB1J0MNGFH.webp', 'image/webp', 'public', 'public', 57948, '[]', '[]', '[]', '[]', 1, '2025-06-30 08:44:29', '2025-06-30 08:44:29');

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int UNSIGNED NOT NULL,
  `migration` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2025_03_06_112623_add_two_factor_columns_to_users_table', 1),
(5, '2025_03_06_112632_create_personal_access_tokens_table', 1),
(6, '2025_03_06_112632_create_teams_table', 1),
(7, '2025_03_06_112633_create_team_user_table', 1),
(8, '2025_03_06_112634_create_team_invitations_table', 1),
(9, '2025_03_06_135327_create_accommodations_table', 1),
(10, '2025_03_06_135343_create_bookings_table', 1),
(11, '2025_03_07_000000_add_user_id_to_accommodations_table', 1),
(12, '2025_03_07_131939_create_imports_table', 1),
(13, '2025_03_07_131940_create_exports_table', 1),
(14, '2025_03_07_131941_create_failed_import_rows_table', 1),
(15, '2025_03_10_135811_add_accomodation_prices_table', 1),
(16, '2025_03_11_000000_create_booking_statuses_table', 1),
(17, '2019_05_03_000001_create_customers_table', 2),
(18, '2019_05_03_000002_create_subscriptions_table', 2),
(19, '2019_05_03_000003_create_subscription_items_table', 2),
(20, '2019_05_03_000004_create_transactions_table', 2),
(21, '2024_06_10_000000_create_accommodation_groups_table', 2),
(22, '2024_07_15_000000_create_promotions_table', 2),
(23, '2025_03_13_131659_create_accommodation_anavailable_period_table', 2),
(24, '2025_04_07_063443_create_media_table', 2),
(25, '2025_04_07_125829_add_soft_deletes_to_accommodations_table', 2),
(26, '2025_04_11_121234_add_occupancy_fields_to_accommodations_table', 2),
(27, '2025_04_11_132115_add_occupancy_to_bookings_table', 2),
(28, '2025_04_12_165148_add_contact_details_to_bookings_table', 2),
(29, '2025_04_12_173909_add_minimum_booking_notice_to_accommodations_table', 2),
(30, '2025_04_16_135040_create_plans_table', 2),
(31, '2025_04_16_135041_create_plan_features_table', 2),
(32, '2025_04_16_135042_create_plan_subscriptions_table', 2),
(33, '2025_04_16_135043_create_plan_subscription_usage_table', 2),
(34, '2025_04_16_135044_remove_unique_slug_on_subscriptions_table', 2),
(35, '2025_04_16_135045_update_unique_keys_on_features_table', 2),
(36, '2025_04_17_000000_add_accommodation_group_id_to_accommodations_table', 2),
(37, '2025_04_29_155616_create_user_settings_table', 2),
(38, '2025_05_01_000000_add_team_id_to_accommodations_table', 2),
(39, '2025_05_05_130611_create_notifications_table', 2),
(40, '2025_05_08_062337_add_notes_to_bookings_table', 2),
(41, '2025_05_08_084029_create_sites_table', 2),
(42, '2025_05_12_080735_create_accommodation_group_prices_table', 2),
(43, '2025_05_16_125255_add_is_published_to_accommodation_groups_table', 2),
(44, '2025_05_17_155100_rename_active_and_is_published_columns', 2),
(45, '2025_05_17_155723_rename_active_and_is_published_columns', 2),
(46, '[timestamp]_create_accommodation_searches_table', 2),
(47, '2025_05_30_120000_create_blogs_table', 3),
(48, '2025_06_05_093517_add_minimum_stay_to_accommodation_table', 4),
(49, '2025_06_15_000000_add_newsletter_opt_in_to_users_table', 5),
(50, '2025_07_01_000000_create_categories_table', 6),
(51, '2025_07_01_000001_create_blog_category_table', 6),
(52, '2025_06_18_000001_add_analytics_indexes_to_accommodation_searches', 7),
(53, '2025_06_24_083035_make_accommodation_group_id_required_on_accommodations_table', 8);

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `notifiable_id` bigint UNSIGNED NOT NULL,
  `data` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `read_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `type`, `notifiable_type`, `notifiable_id`, `data`, `read_at`, `created_at`, `updated_at`) VALUES
('0b97abb5-a16c-4ca4-b0f3-7f4bbb58ef1e', 'App\\Notifications\\NewBookingNotification', 'App\\Models\\User', 1, '{\"booking_id\":3,\"accommodation_id\":10,\"accommodation_name\":\"The Sleepy Den Lodge\",\"guest_name\":\"Riaan Laubscher\",\"start_date\":\"2025-07-28\",\"end_date\":\"2025-07-31\",\"total_price\":\"3600.00\",\"booking_status\":\"Pending\",\"booking_status_color\":\"#f59e0b\",\"message\":\"New booking received for The Sleepy Den Lodge\"}', '2025-06-18 09:52:25', '2025-06-12 11:38:17', '2025-06-18 09:52:25'),
('25673297-12cc-487a-b915-b0c45985a8f2', 'App\\Notifications\\PlanChangeNotification', 'App\\Models\\User', 1, '{\"old_plan_id\":1,\"old_plan_name\":\"Free\",\"new_plan_id\":2,\"new_plan_name\":\"Pro\",\"new_plan_price\":249,\"new_plan_currency\":\"ZAR\",\"new_plan_interval\":null,\"message\":\"Changed from Free to Pro plan\",\"icon\":\"credit-card\",\"color\":\"#009B77\",\"is_cancellation\":false}', '2025-06-11 11:16:14', '2025-06-11 10:34:16', '2025-06-11 11:16:14'),
('9126cc30-ec71-4fdd-bb87-49908c96c730', 'App\\Notifications\\PlanChangeNotification', 'App\\Models\\User', 5, '{\"old_plan_id\":null,\"old_plan_name\":null,\"new_plan_id\":1,\"new_plan_name\":\"Free\",\"new_plan_price\":0,\"new_plan_currency\":\"ZAR\",\"new_plan_interval\":null,\"message\":\"Subscribed to Free plan\",\"icon\":\"credit-card\",\"color\":\"#009B77\",\"is_cancellation\":false}', NULL, '2025-06-06 10:04:52', '2025-06-06 10:04:52'),
('cd6da36d-3f9d-41b6-9005-e9bfc67f8b33', 'App\\Notifications\\NewBookingNotification', 'App\\Models\\User', 5, '{\"booking_id\":1,\"accommodation_id\":8,\"accommodation_name\":\"Bay 2\",\"guest_name\":\"John Doe\",\"start_date\":\"2025-05-29\",\"end_date\":\"2025-05-30\",\"total_price\":\"2500.00\",\"booking_status\":\"Confirmed\",\"booking_status_color\":\"#10b981\",\"message\":\"New booking received for Bay 2\"}', NULL, '2025-06-06 10:04:52', '2025-06-06 10:04:52'),
('d9315113-8fe6-4303-915c-c0799ec302d1', 'App\\Notifications\\PlanChangeNotification', 'App\\Models\\User', 1, '{\"old_plan_id\":2,\"old_plan_name\":\"Pro\",\"new_plan_id\":1,\"new_plan_name\":\"Free\",\"new_plan_price\":0,\"new_plan_currency\":\"ZAR\",\"new_plan_interval\":null,\"message\":\"Changed from Pro to Free plan\",\"icon\":\"credit-card\",\"color\":\"#009B77\",\"is_cancellation\":false}', '2025-06-11 11:16:14', '2025-06-11 09:47:45', '2025-06-11 11:16:14'),
('fa09382d-98c1-4ba9-b14e-0404dae412fb', 'App\\Notifications\\NewBookingNotification', 'App\\Models\\User', 1, '{\"booking_id\":2,\"accommodation_id\":1,\"accommodation_name\":\"Koss, Heller and Schimmel\",\"guest_name\":\"Riaan Laubscher\",\"start_date\":\"2025-07-01\",\"end_date\":\"2025-07-03\",\"total_price\":\"800.00\",\"booking_status\":\"Pending\",\"booking_status_color\":\"#f59e0b\",\"message\":\"New booking received for Koss, Heller and Schimmel\"}', '2025-06-11 11:16:14', '2025-06-06 10:04:53', '2025-06-11 11:16:14');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `tokenable_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `abilities` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `personal_access_tokens`
--

INSERT INTO `personal_access_tokens` (`id`, `tokenable_type`, `tokenable_id`, `name`, `token`, `abilities`, `last_used_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 'App\\Models\\User', 4, 'widget-auth-nvLyffsUQCOu', '624c9aae6c4d42f4a4e0f30f20edb58a8671aa47f95ddbf420f24825f2adee42', '[\"widget-access\"]', '2025-05-23 14:42:50', NULL, '2025-05-23 13:37:17', '2025-05-23 14:42:50'),
(2, 'App\\Models\\User', 1, 'widget-auth-RfSfF0lEAq6e', '313858175d8280ab5b9191d6c3c6b186770a5fbb0b0a6c3bf9022c8d79930a65', '[\"widget-access\"]', '2025-06-06 09:50:09', NULL, '2025-06-06 08:02:42', '2025-06-06 09:50:09'),
(3, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '7a45b6662277c59681af83b2880c256820f9737f8e04a586da97b26ad1520cd0', '[\"widget-access\"]', '2025-06-24 17:30:10', NULL, '2025-06-12 10:42:58', '2025-06-24 17:30:10'),
(4, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', 'de63991a62a9c5a8859c020d856554ff2e9895f1e629c451d2c5040c9d91045a', '[\"widget-access\"]', NULL, NULL, '2025-06-27 17:32:50', '2025-06-27 17:32:50'),
(5, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '119bda573d2317170272671fe974259f19f55ec7ee7a8b559e7fb2a960c200ab', '[\"widget-access\"]', '2025-06-28 07:15:48', NULL, '2025-06-28 07:15:47', '2025-06-28 07:15:48'),
(6, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '375cfca30a51bcc261ba603a5e2fe33bd91f67f2cf03a931ca77bbaee2d1a302', '[\"widget-access\"]', NULL, NULL, '2025-07-01 13:56:18', '2025-07-01 13:56:18'),
(7, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', 'b77796e1d8ea8d93249693c982d3f90cc6456f66fd9c4ce966ba1f2c4a9e6170', '[\"widget-access\"]', '2025-07-02 10:01:17', NULL, '2025-07-02 10:01:17', '2025-07-02 10:01:17'),
(8, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', 'd9b88e1b800bc312a910b7da10171d7c258a82012bb9842113c5792b8bfa3e36', '[\"widget-access\"]', NULL, NULL, '2025-07-02 13:01:32', '2025-07-02 13:01:32'),
(9, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '30b7eaa0e7c1b9b4fe6bbb67a03ac0ea3ae4e7c245482635bdabd41f0d36b068', '[\"widget-access\"]', NULL, NULL, '2025-07-02 13:02:44', '2025-07-02 13:02:44'),
(10, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '508668e25d5525e80bae6a6346704941b8445ad7c622418d3c662fc62e982df5', '[\"widget-access\"]', NULL, NULL, '2025-07-04 10:10:52', '2025-07-04 10:10:52'),
(11, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '0c5e645b90e847978b8cdcc6f7676d275693dc8d77ff40fd3d86af307b5f48e4', '[\"widget-access\"]', '2025-07-05 07:33:24', NULL, '2025-07-05 07:33:24', '2025-07-05 07:33:24'),
(12, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '6cc7dfa62c3936f2c3268fdc3557350375e501516fdb876fd4969b0091303b5b', '[\"widget-access\"]', NULL, NULL, '2025-07-07 07:05:44', '2025-07-07 07:05:44'),
(13, 'App\\Models\\User', 1, 'widget-auth-xyZYMpZQWL3n', '0e94071b924ce5635fd2e9617edeaf1261bac6a992462a2a725d0d464bdb2c45', '[\"widget-access\"]', NULL, NULL, '2025-07-07 07:28:58', '2025-07-07 07:28:58');

-- --------------------------------------------------------

--
-- Table structure for table `plans`
--

CREATE TABLE `plans` (
  `id` bigint UNSIGNED NOT NULL,
  `name` json NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` json DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00',
  `signup_fee` decimal(8,2) NOT NULL DEFAULT '0.00',
  `currency` varchar(3) COLLATE utf8mb4_unicode_ci NOT NULL,
  `trial_period` smallint UNSIGNED NOT NULL DEFAULT '0',
  `trial_interval` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'day',
  `invoice_period` smallint UNSIGNED NOT NULL DEFAULT '0',
  `invoice_interval` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'month',
  `grace_period` smallint UNSIGNED NOT NULL DEFAULT '0',
  `grace_interval` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'day',
  `prorate_day` tinyint UNSIGNED DEFAULT NULL,
  `prorate_period` tinyint UNSIGNED DEFAULT NULL,
  `prorate_extend_due` tinyint UNSIGNED DEFAULT NULL,
  `active_subscribers_limit` smallint UNSIGNED DEFAULT NULL,
  `sort_order` smallint UNSIGNED NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `plans`
--

INSERT INTO `plans` (`id`, `name`, `slug`, `description`, `is_active`, `price`, `signup_fee`, `currency`, `trial_period`, `trial_interval`, `invoice_period`, `invoice_interval`, `grace_period`, `grace_interval`, `prorate_day`, `prorate_period`, `prorate_extend_due`, `active_subscribers_limit`, `sort_order`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, '{\"en\": \"Free\"}', 'free', '{\"en\": \"Basic free plan with limited features\"}', 1, 0.00, 0.00, 'ZAR', 0, 'day', 1, 'month', 0, 'day', NULL, NULL, NULL, NULL, 1, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL),
(2, '{\"en\": \"Pro\"}', 'pro', '{\"en\": \"Advanced plan with more features for growing businesses\"}', 1, 249.00, 0.00, 'ZAR', 0, 'day', 1, 'month', 0, 'day', NULL, NULL, NULL, NULL, 2, '2025-05-23 08:52:22', '2025-05-23 08:52:22', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `promotions`
--

CREATE TABLE `promotions` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_available` int NOT NULL DEFAULT '0',
  `used_count` int NOT NULL DEFAULT '0',
  `is_active` tinyint(1) NOT NULL DEFAULT '1',
  `config` json DEFAULT NULL,
  `starts_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `promotions`
--

INSERT INTO `promotions` (`id`, `name`, `code`, `type`, `total_available`, `used_count`, `is_active`, `config`, `starts_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 'First 10 Users Pro Trial', 'FIRST10PRO', 'free_trial', 10, 3, 1, '{\"plan_name\": \"Pro\", \"duration_days\": 60, \"discount_percent\": 100}', '2025-05-23 08:52:22', NULL, '2025-05-23 08:52:22', '2025-06-06 07:59:00');

-- --------------------------------------------------------

--
-- Table structure for table `promotion_users`
--

CREATE TABLE `promotion_users` (
  `id` bigint UNSIGNED NOT NULL,
  `promotion_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `status` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'applied',
  `applied_at` timestamp NOT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `promotion_users`
--

INSERT INTO `promotion_users` (`id`, `promotion_id`, `user_id`, `status`, `applied_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(2, 1, 4, 'applied', '2025-05-23 13:26:57', '2025-08-21 13:26:57', '2025-05-23 13:26:57', '2025-05-23 13:26:57'),
(3, 1, 1, 'applied', '2025-06-06 07:59:00', '2025-08-05 07:59:00', '2025-06-06 07:59:00', '2025-06-06 07:59:00');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_id` bigint UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `user_agent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `payload` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `last_activity` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('140l2zrZ2vdXcUMTd3225CN7Fk1jwEx65OyRCxUM', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiMVhjWTMwT2NEcmI2WDdtNE00Mndpc2hIRmVkb1l2SXpKd0YxaVJLZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751870127),
('1RNFr9wTkIpfudV6t9aZSKFSYcfuEW3kQcLm5WSt', NULL, '***********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/114.0.1823.43', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaHdjMzVaY1FOZkRIbWZjbFJhbGx5UGVNdll5SFVKQXhjSnVJVlRodiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NDA6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphL3Rvb2xzL2RlbW8iO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX19', 1751853664),
('1TSsQVbibec3QAygBGjNHx7zF8sstDtTbCBACezc', NULL, '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTFVzenhNTnp2Z2E3ZVZncXNxa25DYVFpamR0Tk1FR3BkM2d2RXROYSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751854475),
('2wyTZsbHi8jdJsEssDZhz2eAdUr4Y39fzcKDYGlL', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWkdBbFp1cG1SZFk5aXdLUlJ4WFRubHp5RmtRNUpLTGJseWZTV3N3TCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751868398),
('3nqmPmKLIWl1sCa3e4ZIglUPpDOdM3ycvn0rPX4y', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZTk1NFJyVUEwdzdhdGVuelZkY0hzalpRQVpvelpvODJ4NFZGekRpUCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751847065),
('7Bi1XgOirpAcXTH9bXUvtFlcCYbDhjOv9vepXFcR', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoick1PSUpBcm9Ea0FrQkVReENnRTZ6TUpYM0dUY2RMVnJrb2dYMjR2OCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751858595),
('7n98nEfRZMrGhDnCusLucwezUthgAD58qNlKHMTK', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiUG9LMlVkd0xJeGtFNGlyckd6Ym9yTWpQbkM3OENXbml3VTBweHd1ZCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751828515),
('7qILBhd2dj252D6Xgeb0WB7apdNQz6Jpbs86UQ1K', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiVzBnSWptQU91eXdJYzJsTkFnNVFKTGxWY2pnaTg2OE95NHJoeDhlcCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751843569),
('98U9cu899hsYyKLYNv4xtaiel5b1VYbeA5LSEnMo', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoienFBbVRYRFhMSDJWREtRcloxcE1Jc3AwS2dHZEQ2dFJIWmtnRlpRNyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751836073),
('9dIrnrLVayYHLyeZWU5ifpgsXR1kg0ZZmjdK9kuU', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiMFRPZTZBQmJmV3lTMFpyeXQycXJ5bENEZ2FCYmsyWjJleFVoOUUxTiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751839550),
('9S1HzqO3E5GBMmescCYz01Enl90Ot1V4emOejtRz', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSU9oeFg0NU1MSzFGOGlMbjc0dDYwcWVyeXNtN2l4akJxck5ZTjRsMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751862235),
('aHVZz1teyqtGvPylTNKXhHigfzZEsM2yqh0sN4pZ', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSnNYelp6akpaQmVPQlpNdEdqaHV2M0NXbkZ6SnZWdndGdzdaUm5DaiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751857409),
('AIJorwoYRDlhJKKRHiPO2HQpAxRWOCQCKSsBDUsq', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZEtDVGViZnhIZTNkSm16QXI3b0VNSjh2RVEycmRva3ZKN3I3bU9yUiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751853607),
('aiytTtQghQlmwwTAGBcXYzfcPN3f0c2QHgtmkat9', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiblRxdDRHRTRETHpOMzM4TkR6TDdtS2lUOW5YeFY3STFxR3hJMmlmbSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751835630),
('AkGspQcfNKZEVnppMNgoko3byj1xBmMSsnahiJbs', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZWdVUFhWWjN6QWxHVUNvdzRhS3ZZOVhvWkpzdVhpb3lWcGxrMldXSCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********),
('B5mLGxtXPzpqymgHrKgJHXVzqRinOUuxdw5HDb15', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWGQ1Nkp0bDBmc1ZJUnhsWWEwczlWaU9mNmY0VEpDajV3ZjVOVzNiQiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********),
('bZFTZdibaJvkuhV3NZVvVl8JCDwOL5TdIW2wgbGc', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTnhKQ2tQZW1FblZRSE92a2JTNXRpNEdYTDBJUzZSdEVXdjJkWGNFZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********),
('d13ta9qszb4CH17u7lAlM7hlV1hLAIpjZLt70XOZ', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiRlhCSHFSa0dzYXRWUFlNNVBLUGdPU2E1aWlwVVZxNm12dXl3TFdtaSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', **********),
('ebQVbcA55umJzLV0c7MkmptsJK5ZyzBXbhqrIlUr', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiMzZ4MzdCbHQzaW1RWHB1YnB1SmRrUWlIbUhXb0FWRUp4YjJobDJldyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751866250),
('Ep2eLYEV0Q2uBwLkNOhn0I98cuFvNaP0CJb3yiy8', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZHQ0WXQ4VmtTVWpzM1UxUnlvcFBKbmlzc1B3RXdVS3BXbm9jbjFzMiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751861030),
('evEpGGwzXnEG5rg3vVC23yobcYg9jnixeXA9fHat', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiU3NkUWh4bFV5RHZLT0RqRXNGcmRleUs5WUdLR3liM1d5RHJTQksxNiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751854869),
('f2HmLZOCAEFmQOC5vT11BIDsWUdBWN1Tdj8jptLw', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiUlNGb0hFTjdTWDJySW1GeklNZjhSM1ZDbVFkVGJqRWU1b2Y1dkdNeCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751856123),
('FDAHWKpKb2q1d1bfppmcbGiaRkWiFyQX5wB6mKkf', NULL, '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoid3BLRjN5c29DNGtuem5USjRPRm1GTWxPeURhNEJiS3pKMUppczNIZCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751851908),
('FgzWL4uN0GE8nOZSODBDqL20gyFrUI3q3Ae4ChC4', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiYVdNckJwOUpzbUs3VkkwWnBBYkJSTGNtU0o2ZUZLMURxUEZwbjJQUiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751849864),
('FHH7wTxAbzngCV6DWekljUg728lbPCNvK5FEwYk6', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiUWZnSlFQcG5pd1FvcUNCOVdTak1QMGZtWEpMNGQ5b1hxV1VKS3JyTiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751870863),
('FIQ5zJpSRL8iHcR1OLSylGzRKE8iYtRc0Hs9CBwO', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiREd3NDNLcktrcVlOcm1lQzJiU3hIVzNlYXpSZ0xmT1F5TjhSc2NTZSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751873374),
('fP7T4l7EftlX0S8MATrHW2TraHpHYwFwrtlxvsSY', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidEtvUlYxU2c1WTVNTFhoWmhMNEhESHNsVzNmN0RrQUtUYWtEeklzcyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751852330),
('fTvjcFWipZEQyxuj7URZPr0NYUvpY35LX4xOCUNU', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiSnlRN3J3d1RDQ1p2dXl0amlhMEVoYkN6ekhyV3pJdm93ZkMxZW5VdiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751848599),
('fyA5GBpkVdPZ9Fd8DEkvFWqAPcikdOFj0iIoAv96', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTWt5aXdlbTV6dnA1dmpVTFNGMUk4akpUbTBFMERCd3VvTnlLSEdWRSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751826036),
('FzjM3MUFtif9IwPVWVbLWpkGQLeivrZSlyabWvAW', NULL, '**************', '\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNVlNOXVvYmUxaHR6ejFUdm9zeDFOUllNeHJFNUZzdlAzb2JtMm1ybyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751823136),
('GfBL75xO1RKVKGEe9rPAd4IPVNY1eyZyAOkg5Pkc', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoickRLT3lIRGhRVmJYMDRGTmlBZ1VHSDFxSzY5bWt0c3RsNjBKVm5oSCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751842303),
('Gxd9o6s23Tiv5PfFMODGBg6YIp9NpNEEnZIkqkxf', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiVW1WeFNjZWtCNElaZ0VOV245TFJ2VEgxdU9wUmdrMHQ0MjMzenFuWCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751814856),
('HbZSLM05bKLGZ4LN2tIlWIhECD75z2YLCr612qaL', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTGl4WGJ0QVpaZ2R6cVd4ckI3YUJNeVRHVDZBajNmaGpkVHFVT0xnRiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751872108),
('HGCYmexnZrLhBbWKtGAbCUcdMRmQs4YT4iKKGIf0', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaUNoZnlsVHQweHdleGpwdHJ2RjduMmsxUk5aajR1TGNobFdFaUlGWiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751831740),
('KFnSQPuSxJhS3YJyJ0C0P3Wjyo4NFpabW7AOvcYY', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWlFEOVpPbnJYdTRtZ3lCMjlyaFJTWXVVWXNob3ZtcjJBMFFwQVBJbSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751829751),
('KGX1HrBQtkqioDmVEFnItKdzjuNJp7Fkb9lFjL8w', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQkdxRXo3MklJbmYzNjBuTFNROFVxS3lxOEpKRks2alp5STEyOE92TyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751844838),
('l7myWOBKg26vIlrHkC7aEOCw1gRLwfr4Zc2D0RMu', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiOUpvVzhoRmJUYWxuc011bjlGTFNsTHhFVUtCZUNINHFtZVVkWVN2NCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751817386),
('lcKcxKnfTFU2uHF5wdXiZwjzSWxCzXXlGja9b1wy', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoieUJjSVhkY3B0WHJybTcyOHVxRmRPWjhteGx5c3VVOHV3TXhkOHZIMSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751837308),
('LYwRXcNuPxqyGmopHInqTr87div4HjWDAtdPJ60d', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidEFURzUydXFvaHVOWGc1UmgwVnlHdHgycXZqWldURTVlczhKMFpiaSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751834788),
('lzFDgYcTxPSL4Xs60XQ1s4j4rljOK21e6li1cbAB', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiRGhEV3NBWUpkNWJ3U29VdWhOQ0xscHhOVmJxbGNwSXpST2dLNkdkZSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751838537),
('m5y08hbopBq9dBlYXrmx1kPJYHlv7ISZjQcWOoKm', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZDZPYnY1bFh1SlREaWdacEhTZUVVSnFtYjg2aTlPeURHTmZNeE8xeiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751863441),
('MngM6gQeHX28kaUwPaxqlayqr4PpMXWsNR09c8Ak', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoickpSdWdzRnZGRG5TRm5mekFGN3hRbGttbUJuVDhhOXNwelFSbDRpdSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751819886),
('mRhocB46JfGuzivHXLm804T43qzuxRhWYjiEIhhP', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiU1lZUEtFRWtWR1ViUEtuTnpXNVQ3aW4xc3pTVXhDbkFFRlZ0YlJoOSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751858746),
('N6uPw0Tb3QWzNXnkIqJ6hsQCgEc6PJWj67xZSCWP', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaGFmWHViT3BuVEhwc0xaY2g0ZzQ5TjB1ZllFUjVBQU5TcE9xVHI1NyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751823534),
('n9mJKf37XHDG9wzvgOApkJSCeUl1K8Di5vgiiosZ', NULL, '101.36.108.133', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/550.46 (KHTML, like Gecko) Chrome/88.0.222 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiU2J2S0Nvc3d3VXg3MVRySlNpWjRBZm9nR2c5WGtYTzRQajBTRnNSeCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751848037),
('NIQwf3XBMxQhIIjoIvbc9S6ecxF1mE89BEF4JWhL', NULL, '52.50.193.74', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTVg0eXBvbWlsbmxUOWpVaVFaNXVnOWh1dXpSbE01ZGRZbVNvc1FyZyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751865174),
('nlEYHKvW719TmzdKeWZIuJlSVIQSaeSb2FPuptCb', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQW1Rb2FyVmZhQVN6ejIxUUZyM25vRUxtakMxVk5NVjVZTzFTOHp4RiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751832265),
('nT0d2LwSzvYmX7x9Fb4TO27bsgaVNV63vljtA5yz', NULL, '178.132.82.79', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/78.0.3904.108 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoicjlKckprWnQyMjVXa0lLNFBvMzByRjY4Z0M0bUYyRXpjQ3kxRU55aCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NjA6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphLz9YREVCVUdfU0VTU0lPTl9TVEFSVD1waHBzdG9ybSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1751862383),
('NZLNYoaxTWx3eiEfIBzJtwJPFIwPCcg8X2FaoLY2', NULL, '2602:80d:1000::3e', 'Mozilla/5.0 (compatible; CensysInspect/1.1; +https://about.censys.io/)', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTjFrREhJN3c3dU4xMHR0WnhlU3Rtd0JuTmdxT3VjanVEcVBvUmdiOCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751821344),
('oEC5xUum7MephNW91eKe5dCD2Vpqy0CD2vtvZaxM', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidHB5M0xhVGViMGltY1lidlI1N0RLelhUZEVyZEZlWndRdlBjc3RnbCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751843423),
('OfoKCZYsUp8pWNK3i9NHAMtjCRvzRudTRAgvsaAK', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiTTBSWnpKMUtTV0xqak9CcVRObWZGcm9pRFh0NmloMExubXdRSlJWbCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751859823),
('Ppnt13SZuBG6P2IPRMWmRbNPScv7CU6mwbW8JBOB', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiM0RHa044SlJWNlU4OW9JUHFVbU15UVJXWTNLeFZkM2pzbEJlaGgxUiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751827851),
('PqHgrgfbPXAtrzSUkU94b9Sy8iVMibdaDOrs6FYx', NULL, '************', 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P; Google-AdWords-Express) AppleWebKit/[WEBKIT_VERSION] (KHTML, like Gecko) Chrome/[CHROME_VERSION] Mobile Safari/[WEBKIT_VERSION]', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWTU1cnRuQmhnbFRHeno4dUk5NkFGc1RNV21GVktlbE95MnM4OGd1ZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751872728),
('PR1W5LtX0M1ecumKOxPo2cehJMdyzO6UDTD5pMhi', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQzAyZnQ5cmt0NkQzc1JFZWpRakhLQWkzaGh6RFByNU9ldkdPWEh1NSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751820029),
('pwD9spv04NsAYbafEoMaRcqWL3z5qZzQ5Qy4eZB2', NULL, '************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) Chrome/126.0.0.0 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaDNya09YZWZuR2lNbjJlbmxHZUJpWXIzMkd1TTJHUkIzRG5xbmpnZiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751855134),
('QDvUsjZRyEwLik1kFHnCealbUFjGTFo9AWU1RGF3', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiM3FwM2VadlQ5dHVtNEJFbmhwTEhsUkVvMzU4bnhXUlcxOXF5NllYeiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751867166),
('QryLLAc61jNNbXuzfvdEetDLcLdvqKcClpvcAXyX', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZEVXaU8ybzZ6ZzZuSmd6WGd3U1JFdnFBUHNMb3BXZmpiVmxENzF0ViI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751830996),
('rJtsfTtwz16zCeBqChDIxyhVvA1zi5PKSYhH4WKl', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiaVhSSE9wMlh0cGZyalJKcXRyOWtmZkY2dFhhaFVkdW54alpzTTR2TSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751816103),
('RsWcYJbOTeHlhF0P9mhUf8VAwFqmlI1YyMnVaoR6', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidUNxMDJQZXBoT1VqcEIzVzl3ejdwdXh0QWhGMTA1cm1FQzJhQlo4SCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751824771),
('s3Zj0TgWd3yu1OKrOeCaGrFGsMkzz4tGgudHMZhS', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiMnh1aUNRbmpaRTlOVVVKc3lrTjlhclQ5Y09DUUV3RFVwY0JYdmdwTSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751874627),
('SVasPFKfZudkQ53jTzPWdClJoBHWvaaWR0XoadXL', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiR2tLcVN3NmRDZ0V0aHRlZkpCRTdtY25TWjhEeHF1OFQ4c3M2cnZDSyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751865935),
('TmcSVRm7Yea5T6hC26hdcPjNca6vK78pKfi8ECmQ', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiRVJ5UUF6Y1pNbUg1V0hNTDVWcGFTc0JXM3cwQm9TRmRXN3N0RjFsWCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751833517),
('u7swYUgKsrois3p75ekKtJhW3kYVZwKmcE63emK1', 1, '102.132.253.86', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36', 'YTo1OntzOjY6Il90b2tlbiI7czo0MDoiMnNNb1hraU9ZeVBXRHB1U0xxZXhEVkhxN1lldm94aUpCdlVqVlA1ZyI7czo1MDoibG9naW5fd2ViXzU5YmEzNmFkZGMyYjJmOTQwMTU4MGYwMTRjN2Y1OGVhNGUzMDk4OWQiO2k6MTtzOjk6Il9wcmV2aW91cyI7YToxOntzOjM6InVybCI7czo0MToiaHR0cHM6Ly93d3cuYm9va2luZ2JlYXIuY28uemEvYWRtaW4vYmxvZ3MiO31zOjY6Il9mbGFzaCI7YToyOntzOjM6Im9sZCI7YTowOnt9czozOiJuZXciO2E6MDp7fX1zOjE3OiJwYXNzd29yZF9oYXNoX3dlYiI7czo2MDoiJDJ5JDEyJFZmRS54SW1mZG5qV1hyZW4xem16bC41UFRndGpNZ21hS3A5S1ZLTW9wVjMxVjJ6YXRiSlFXIjt9', 1751873940),
('uQjOyn31efG5SCDo19OsRb0JbQMD2cxTWxwdPnGf', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNjg5VENuOTdaUEtwd3VWdlY1Z3pxN2ZOWVlYZGFTQ01LY1djR2RDVCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751846118),
('vsZdMR7VB4lztnJMEhuTtUiIdgCHiMeBysexoVi9', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidDBHVlc0cDJuRmxqMXhlZkdhMXZSMVdkZG5JRWtGV3R2Nlg2UlRJMyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751841050),
('w4CpNgRoSAaeSyJubWnvlU4APpRDBaPsFheqo1s3', NULL, '66.249.83.36', 'Mozilla/5.0 (Linux; Android 6.0.1; Nexus 5X Build/MMB29P; Google-AdWords-Express) AppleWebKit/[WEBKIT_VERSION] (KHTML, like Gecko) Chrome/[CHROME_VERSION] Mobile Safari/[WEBKIT_VERSION]', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoieDNxRFY3ZUs5RTRrcjNQUEduemNwRktFQ0dtS0RhbWNKNVRXNXI1MSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751872726),
('WfBpUjubTEj2JpJ0VfV4HWBLsmanGTOnVlfKBqli', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoicGpWbDJtYTBwVkJLcUFvQ25JR0hUQzdVd015MUVOam1vaTRLdW43MiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751851105),
('wIljhBl6qzE3q4sDUsUp67nUHkFoH4QVgNbgoND2', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiQUpEQlI5b2pTSVNKeU9oN09mam5tRDRjZEVTaXZYYXk0MlFiSmRIMCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751821102),
('WjknKxTGAEFQ5XxIyqTunXwnNh5lPK3rVdjND3Es', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiS2xQQkNlaHM4ZEIzZ2ZkSXVzb0hpTWI0VWI3RkI2WWlhS2dwN2ZUOSI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751854859),
('wlaXpy7pEZiUAjKweEw4TKkpkSIGU7CEIOLkg40G', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidFFlWDhicXc1S3FWMktRdDQwbVBlNWNDaG04SmpxQzg1MXdoWGRxYyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751839793),
('WWGu6MeARwhPyBObkXLCKBBvGXSOFmQjcTKcGWBW', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiWmdMaWVndkhjRjJBRXphOE5iTjlJR2pWRHpqMTQ5YjRRWFpmbHJYUyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751818658),
('XLBcOkkbvCWtsOoG1MF5RBD7RDTQGiMoa5QRTnmG', NULL, '3.208.116.168', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiY0xEakxxV2kzbVo5WWp1OU42YTZKZVNkR296cFQxZkRaZEVMSE0xdiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751864349),
('XMW5Bpg389yqyctU8L5EcQf0BYreQyALgMUziZ22', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiMml0ZzJITU1aUm9VSW5XUElvVGhiZzg1QkJ4ZWNCRW00Q2lxbUF6ciI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751847357),
('yg17SpfKf7OsokjXQstzZfiSUuDFAc70rHldNGE7', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidk11ZjRaRkRaUXFDNFVja2F1RFdYRndETUhTYmQ5eHVHWUQ0ZmhybiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751869607),
('yotYZk47AwsCKTXWKcSKb7Vo6RD2dd48Jy5o1HE3', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNVh1dkVqelRJZjNCNFBIVmhEYjhCaHBHQzNSaEZ0QVlXeEZrU0dsaCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751874063),
('z1e1pWvezUJXYtvP7vSqX5VRsBAgrbQ5Q8RL74Iq', NULL, '2602:fa59:10:ad6::1', 'python-requests/2.32.4', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiZHg5UUlUNUVFSWFTT2RqdEVCZHFRZnd4QVpiQzlKd0JBREdXdkttNCI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751864692),
('z2wxQRV1UWlHCuGbqO0DtzjCZBS1dQ11qy1wABBN', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoicFo3S3V2Sm5rb0liV0Zxd2tDSXVtY1NRcllybnBlRXpobzkwWU9xNiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751850957),
('zJHS26kqWMiXvnthHIUk22vnl1S4mfwrTY1RIgSm', NULL, '2a01:4ff:1f0:82a2::1', 'SaaSHub', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoidGNpSVFGd3NpZGxWa2xJQkFscEE0bFFUYWIzanZtSFhXakhWTmRiMiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mjk6Imh0dHBzOi8vd3d3LmJvb2tpbmdiZWFyLmNvLnphIjt9czo2OiJfZmxhc2giO2E6Mjp7czozOiJvbGQiO2E6MDp7fXM6MzoibmV3IjthOjA6e319fQ==', 1751816125);

-- --------------------------------------------------------

--
-- Table structure for table `sites`
--

CREATE TABLE `sites` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `site_id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `domain` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `api_key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `published` tinyint(1) NOT NULL DEFAULT '1',
  `last_used_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sites`
--

INSERT INTO `sites` (`id`, `user_id`, `name`, `site_id`, `domain`, `api_key`, `published`, `last_used_at`, `created_at`, `updated_at`) VALUES
(1, 4, 'Lovepup', 'nvLyffsUQCOu', 'lovepupplus.lndo.site', '1|78LLajaKwDxyaksIuBNzsqygDjLuJmZNIGvkGFFAb2be4b98', 1, '2025-05-23 14:42:50', '2025-05-23 13:36:20', '2025-05-23 14:42:50'),
(4, 1, 'Demo', 'xyZYMpZQWL3n', 'www.bookingbear.co.za', '13|eywFfWxwTifcK6glNrFahMFpBAguuDHbuWxaIRsI0025510b', 1, '2025-07-07 07:28:58', '2025-06-12 10:42:16', '2025-07-07 07:28:58');

-- --------------------------------------------------------

--
-- Table structure for table `subscriptions`
--

CREATE TABLE `subscriptions` (
  `id` bigint UNSIGNED NOT NULL,
  `subscriber_type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `subscriber_id` bigint UNSIGNED NOT NULL,
  `plan_id` bigint UNSIGNED NOT NULL,
  `name` json NOT NULL,
  `slug` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` json DEFAULT NULL,
  `timezone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `trial_ends_at` datetime DEFAULT NULL,
  `starts_at` datetime DEFAULT NULL,
  `ends_at` datetime DEFAULT NULL,
  `cancels_at` datetime DEFAULT NULL,
  `canceled_at` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `subscriptions`
--

INSERT INTO `subscriptions` (`id`, `subscriber_type`, `subscriber_id`, `plan_id`, `name`, `slug`, `description`, `timezone`, `trial_ends_at`, `starts_at`, `ends_at`, `cancels_at`, `canceled_at`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'App\\Models\\User', 1, 2, '{\"en\": \"main\"}', 'main', NULL, NULL, '2025-05-23 08:52:22', '2025-05-23 08:52:22', '2025-08-05 07:59:00', NULL, NULL, '2025-05-23 08:52:22', '2025-06-12 08:17:28', NULL),
(2, 'App\\Models\\User', 2, 2, '{\"en\": \"main\"}', 'main-1', NULL, NULL, '2025-05-23 08:55:06', '2025-05-23 08:55:06', '2025-08-21 08:55:06', NULL, NULL, '2025-05-23 08:55:06', '2025-05-23 08:55:06', NULL),
(3, 'App\\Models\\User', 4, 1, '{\"en\": \"main\"}', 'main-2', NULL, NULL, '2025-05-23 13:26:57', '2025-05-23 13:26:57', '2025-08-21 13:26:57', NULL, NULL, '2025-05-23 13:26:57', '2025-06-11 13:38:04', NULL),
(4, 'App\\Models\\User', 5, 1, '{\"en\": \"main\"}', 'main-3', NULL, NULL, '2025-05-27 09:30:24', '2025-05-27 09:30:24', '2025-06-27 09:30:24', NULL, NULL, '2025-05-27 09:30:24', '2025-05-27 09:30:24', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `subscription_usage`
--

CREATE TABLE `subscription_usage` (
  `id` bigint UNSIGNED NOT NULL,
  `subscription_id` bigint UNSIGNED NOT NULL,
  `feature_id` bigint UNSIGNED NOT NULL,
  `used` smallint UNSIGNED NOT NULL,
  `timezone` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `valid_until` datetime DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `teams`
--

CREATE TABLE `teams` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `personal_team` tinyint(1) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `teams`
--

INSERT INTO `teams` (`id`, `user_id`, `name`, `personal_team`, `created_at`, `updated_at`) VALUES
(1, 1, 'Riaan\'s Team', 1, '2025-05-23 08:52:22', '2025-05-23 08:52:22'),
(2, 2, 'Riaan\'s Team', 1, '2025-05-23 08:54:38', '2025-05-23 08:54:38'),
(3, 3, 'Riaan\'s Team', 1, '2025-05-23 10:26:15', '2025-05-23 10:26:15'),
(4, 4, 'Riaan\'s Team', 1, '2025-05-23 13:26:26', '2025-05-23 13:26:26'),
(5, 5, 'Riaan\'s Team', 1, '2025-05-27 09:29:48', '2025-05-27 09:29:48'),
(6, 6, 'Riaan\'s Team', 1, '2025-06-06 14:31:34', '2025-06-06 14:31:34'),
(7, 7, 'Riaan\'s Team', 1, '2025-06-07 15:13:02', '2025-06-07 15:13:02'),
(8, 8, 'Riaan\'s Team', 1, '2025-06-11 13:51:31', '2025-06-11 13:51:31');

-- --------------------------------------------------------

--
-- Table structure for table `team_invitations`
--

CREATE TABLE `team_invitations` (
  `id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `team_user`
--

CREATE TABLE `team_user` (
  `id` bigint UNSIGNED NOT NULL,
  `team_id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `role` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint UNSIGNED NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `two_factor_secret` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_recovery_codes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `two_factor_confirmed_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `current_team_id` bigint UNSIGNED DEFAULT NULL,
  `profile_photo_path` varchar(2048) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_admin` tinyint(1) NOT NULL DEFAULT '0',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `newsletter_opt_in` tinyint(1) NOT NULL DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `two_factor_secret`, `two_factor_recovery_codes`, `two_factor_confirmed_at`, `remember_token`, `current_team_id`, `profile_photo_path`, `is_admin`, `created_at`, `updated_at`, `newsletter_opt_in`) VALUES
(1, 'Riaan', '<EMAIL>', '2025-05-23 08:52:22', '$2y$12$VfE.xImfdnjWXren1zmzl.5PTgtjMgmaKp9KVKMopV31V2zatbJQW', NULL, NULL, NULL, 'MpOz0EPOWgRBoBoE3vIrTTHD3Dc45AANu9lPI47PPNJPozyDtQZjmv379Uxy', 1, NULL, 1, '2025-05-23 08:52:22', '2025-06-11 11:17:29', 0),
(4, 'Riaan Laubscher', '<EMAIL>', '2025-05-23 13:26:50', '$2y$12$llegZwNJFtc9C/gZf3CCS.HbWVxoYtyKfJHqKTIc2WuxawDescYMW', NULL, NULL, NULL, 'mNnOW8q3REMRhon4pSS8aDCMHh0kLrAPlOiZX7iVLXehf7J8cczADmSP0SKI', 4, NULL, 0, '2025-05-23 13:26:26', '2025-05-23 13:28:44', 0),
(5, 'Riaan', '<EMAIL>', '2025-05-27 09:30:19', '$2y$12$Ws3rfZRNU3Q3ZpJ6QA86OeKRrwK2ZTDmB2/dE3uBrB/FgzyBmjs5a', NULL, NULL, NULL, NULL, 5, NULL, 0, '2025-05-27 09:29:48', '2025-05-27 09:31:02', 0),
(8, 'Riaan Laubscher', '<EMAIL>', '2025-06-11 13:51:59', '$2y$12$ASFe8KKKK0NULpqrWvznuOwwLbAnkhY5TEKnwPjj8yciwdbuRQ1Dm', NULL, NULL, NULL, NULL, NULL, NULL, 0, '2025-06-11 13:51:31', '2025-06-11 13:51:59', 1);

-- --------------------------------------------------------

--
-- Table structure for table `user_settings`
--

CREATE TABLE `user_settings` (
  `id` bigint UNSIGNED NOT NULL,
  `user_id` bigint UNSIGNED NOT NULL,
  `key` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` text COLLATE utf8mb4_unicode_ci,
  `type` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'string',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `user_settings`
--

INSERT INTO `user_settings` (`id`, `user_id`, `key`, `value`, `type`, `created_at`, `updated_at`) VALUES
(1, 1, 'booking_terms_and_conditions', '<p><strong>Please not this booking functionality is for testing purposes only, This is not a real booking.</strong></p>', 'string', '2025-06-12 11:37:34', '2025-06-12 11:37:34');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `accommodations`
--
ALTER TABLE `accommodations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodations_user_id_foreign` (`user_id`),
  ADD KEY `accommodations_team_id_foreign` (`team_id`),
  ADD KEY `accommodations_accommodation_group_id_foreign` (`accommodation_group_id`);

--
-- Indexes for table `accommodation_groups`
--
ALTER TABLE `accommodation_groups`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodation_groups_user_id_foreign` (`user_id`),
  ADD KEY `accommodation_groups_team_id_foreign` (`team_id`);

--
-- Indexes for table `accommodation_group_prices`
--
ALTER TABLE `accommodation_group_prices`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodation_group_prices_accommodation_group_id_type_index` (`accommodation_group_id`,`type`),
  ADD KEY `accommodation_group_prices_start_date_end_date_index` (`start_date`,`end_date`);

--
-- Indexes for table `accommodation_prices`
--
ALTER TABLE `accommodation_prices`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodation_prices_accommodation_id_type_index` (`accommodation_id`,`type`),
  ADD KEY `accommodation_prices_start_date_end_date_index` (`start_date`,`end_date`);

--
-- Indexes for table `accommodation_searches`
--
ALTER TABLE `accommodation_searches`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodation_searches_accommodation_id_searched_at_index` (`accommodation_id`,`searched_at`),
  ADD KEY `accommodation_searches_user_id_searched_at_index` (`user_id`,`searched_at`),
  ADD KEY `accommodation_searches_start_date_end_date_index` (`start_date`,`end_date`),
  ADD KEY `idx_user_searched_available` (`user_id`,`searched_at`,`was_available`),
  ADD KEY `idx_accommodation_searched_available` (`accommodation_id`,`searched_at`,`was_available`),
  ADD KEY `idx_searched_available` (`searched_at`,`was_available`),
  ADD KEY `idx_dates_searched` (`start_date`,`end_date`,`searched_at`),
  ADD KEY `idx_unavailability_reason` (`unavailability_reason`,`was_available`),
  ADD KEY `idx_price_searched` (`quoted_price`,`searched_at`),
  ADD KEY `idx_start_date_accommodation` (`start_date`,`accommodation_id`),
  ADD KEY `idx_end_date_accommodation` (`end_date`,`accommodation_id`);

--
-- Indexes for table `accommodation_unavailable_periods`
--
ALTER TABLE `accommodation_unavailable_periods`
  ADD PRIMARY KEY (`id`),
  ADD KEY `accommodation_unavailable_periods_accommodation_id_type_index` (`accommodation_id`,`type`),
  ADD KEY `accommodation_unavailable_periods_start_date_end_date_index` (`start_date`,`end_date`);

--
-- Indexes for table `blogs`
--
ALTER TABLE `blogs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `blogs_slug_unique` (`slug`),
  ADD KEY `blogs_user_id_foreign` (`user_id`),
  ADD KEY `blogs_published_post_date_index` (`published`,`post_date`),
  ADD KEY `blogs_slug_index` (`slug`);

--
-- Indexes for table `blog_category`
--
ALTER TABLE `blog_category`
  ADD UNIQUE KEY `blog_category_blog_id_category_id_unique` (`blog_id`,`category_id`),
  ADD KEY `blog_category_category_id_foreign` (`category_id`);

--
-- Indexes for table `bookings`
--
ALTER TABLE `bookings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `bookings_accommodation_id_foreign` (`accommodation_id`),
  ADD KEY `bookings_user_id_foreign` (`user_id`),
  ADD KEY `bookings_booking_status_id_foreign` (`booking_status_id`);

--
-- Indexes for table `booking_statuses`
--
ALTER TABLE `booking_statuses`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cashier_subscriptions`
--
ALTER TABLE `cashier_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cashier_subscriptions_paddle_id_unique` (`paddle_id`),
  ADD KEY `cashier_subscriptions_billable_type_billable_id_index` (`billable_type`,`billable_id`);

--
-- Indexes for table `cashier_subscription_items`
--
ALTER TABLE `cashier_subscription_items`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cashier_subscription_items_price_id_unique` (`price_id`);

--
-- Indexes for table `cashier_transactions`
--
ALTER TABLE `cashier_transactions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cashier_transactions_paddle_id_unique` (`paddle_id`),
  ADD KEY `cashier_transactions_billable_type_billable_id_index` (`billable_type`,`billable_id`),
  ADD KEY `cashier_transactions_paddle_subscription_id_index` (`paddle_subscription_id`);

--
-- Indexes for table `categories`
--
ALTER TABLE `categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `categories_name_unique` (`name`),
  ADD UNIQUE KEY `categories_slug_unique` (`slug`),
  ADD KEY `categories_slug_index` (`slug`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customers_paddle_id_unique` (`paddle_id`),
  ADD KEY `customers_billable_type_billable_id_index` (`billable_type`,`billable_id`);

--
-- Indexes for table `exports`
--
ALTER TABLE `exports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `exports_user_id_foreign` (`user_id`);

--
-- Indexes for table `failed_import_rows`
--
ALTER TABLE `failed_import_rows`
  ADD PRIMARY KEY (`id`),
  ADD KEY `failed_import_rows_import_id_foreign` (`import_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `features`
--
ALTER TABLE `features`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `features_plan_id_slug_unique` (`plan_id`,`slug`);

--
-- Indexes for table `imports`
--
ALTER TABLE `imports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `imports_user_id_foreign` (`user_id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `media`
--
ALTER TABLE `media`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `media_uuid_unique` (`uuid`),
  ADD KEY `media_model_type_model_id_index` (`model_type`,`model_id`),
  ADD KEY `media_order_column_index` (`order_column`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notifications_notifiable_type_notifiable_id_index` (`notifiable_type`,`notifiable_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `plans`
--
ALTER TABLE `plans`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `plans_slug_unique` (`slug`);

--
-- Indexes for table `promotions`
--
ALTER TABLE `promotions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `promotions_code_unique` (`code`);

--
-- Indexes for table `promotion_users`
--
ALTER TABLE `promotion_users`
  ADD PRIMARY KEY (`id`),
  ADD KEY `promotion_users_promotion_id_foreign` (`promotion_id`),
  ADD KEY `promotion_users_user_id_foreign` (`user_id`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `sites`
--
ALTER TABLE `sites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `sites_site_id_unique` (`site_id`),
  ADD KEY `sites_user_id_foreign` (`user_id`);

--
-- Indexes for table `subscriptions`
--
ALTER TABLE `subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `subscriptions_subscriber_type_subscriber_id_index` (`subscriber_type`,`subscriber_id`);

--
-- Indexes for table `subscription_usage`
--
ALTER TABLE `subscription_usage`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `teams`
--
ALTER TABLE `teams`
  ADD PRIMARY KEY (`id`),
  ADD KEY `teams_user_id_index` (`user_id`);

--
-- Indexes for table `team_invitations`
--
ALTER TABLE `team_invitations`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_invitations_team_id_email_unique` (`team_id`,`email`);

--
-- Indexes for table `team_user`
--
ALTER TABLE `team_user`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `team_user_team_id_user_id_unique` (`team_id`,`user_id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_settings_user_id_key_unique` (`user_id`,`key`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `accommodations`
--
ALTER TABLE `accommodations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `accommodation_groups`
--
ALTER TABLE `accommodation_groups`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `accommodation_group_prices`
--
ALTER TABLE `accommodation_group_prices`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `accommodation_prices`
--
ALTER TABLE `accommodation_prices`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `accommodation_searches`
--
ALTER TABLE `accommodation_searches`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `accommodation_unavailable_periods`
--
ALTER TABLE `accommodation_unavailable_periods`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `blogs`
--
ALTER TABLE `blogs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `bookings`
--
ALTER TABLE `bookings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `booking_statuses`
--
ALTER TABLE `booking_statuses`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `cashier_subscriptions`
--
ALTER TABLE `cashier_subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cashier_subscription_items`
--
ALTER TABLE `cashier_subscription_items`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `cashier_transactions`
--
ALTER TABLE `cashier_transactions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `categories`
--
ALTER TABLE `categories`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `exports`
--
ALTER TABLE `exports`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_import_rows`
--
ALTER TABLE `failed_import_rows`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `features`
--
ALTER TABLE `features`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `imports`
--
ALTER TABLE `imports`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `media`
--
ALTER TABLE `media`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=54;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `plans`
--
ALTER TABLE `plans`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `promotions`
--
ALTER TABLE `promotions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `promotion_users`
--
ALTER TABLE `promotion_users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `sites`
--
ALTER TABLE `sites`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `subscriptions`
--
ALTER TABLE `subscriptions`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `subscription_usage`
--
ALTER TABLE `subscription_usage`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `teams`
--
ALTER TABLE `teams`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `team_invitations`
--
ALTER TABLE `team_invitations`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `team_user`
--
ALTER TABLE `team_user`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `user_settings`
--
ALTER TABLE `user_settings`
  MODIFY `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `accommodations`
--
ALTER TABLE `accommodations`
  ADD CONSTRAINT `accommodations_accommodation_group_id_foreign` FOREIGN KEY (`accommodation_group_id`) REFERENCES `accommodation_groups` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `accommodations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `accommodations_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `accommodation_groups`
--
ALTER TABLE `accommodation_groups`
  ADD CONSTRAINT `accommodation_groups_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `accommodation_groups_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `accommodation_group_prices`
--
ALTER TABLE `accommodation_group_prices`
  ADD CONSTRAINT `accommodation_group_prices_accommodation_group_id_foreign` FOREIGN KEY (`accommodation_group_id`) REFERENCES `accommodation_groups` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `accommodation_prices`
--
ALTER TABLE `accommodation_prices`
  ADD CONSTRAINT `accommodation_prices_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `accommodation_searches`
--
ALTER TABLE `accommodation_searches`
  ADD CONSTRAINT `accommodation_searches_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `accommodation_searches_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `accommodation_unavailable_periods`
--
ALTER TABLE `accommodation_unavailable_periods`
  ADD CONSTRAINT `accommodation_unavailable_periods_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blogs`
--
ALTER TABLE `blogs`
  ADD CONSTRAINT `blogs_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `blog_category`
--
ALTER TABLE `blog_category`
  ADD CONSTRAINT `blog_category_blog_id_foreign` FOREIGN KEY (`blog_id`) REFERENCES `blogs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `blog_category_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `bookings`
--
ALTER TABLE `bookings`
  ADD CONSTRAINT `bookings_accommodation_id_foreign` FOREIGN KEY (`accommodation_id`) REFERENCES `accommodations` (`id`),
  ADD CONSTRAINT `bookings_booking_status_id_foreign` FOREIGN KEY (`booking_status_id`) REFERENCES `booking_statuses` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `bookings_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`);

--
-- Constraints for table `exports`
--
ALTER TABLE `exports`
  ADD CONSTRAINT `exports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `failed_import_rows`
--
ALTER TABLE `failed_import_rows`
  ADD CONSTRAINT `failed_import_rows_import_id_foreign` FOREIGN KEY (`import_id`) REFERENCES `imports` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `imports`
--
ALTER TABLE `imports`
  ADD CONSTRAINT `imports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `promotion_users`
--
ALTER TABLE `promotion_users`
  ADD CONSTRAINT `promotion_users_promotion_id_foreign` FOREIGN KEY (`promotion_id`) REFERENCES `promotions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `promotion_users_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `sites`
--
ALTER TABLE `sites`
  ADD CONSTRAINT `sites_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `team_invitations`
--
ALTER TABLE `team_invitations`
  ADD CONSTRAINT `team_invitations_team_id_foreign` FOREIGN KEY (`team_id`) REFERENCES `teams` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `user_settings`
--
ALTER TABLE `user_settings`
  ADD CONSTRAINT `user_settings_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
