<?php

namespace Tests\Feature;

use App\Models\Blog;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class BlogSeoTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create();
    }

    public function test_blog_can_be_created_with_seo_fields(): void
    {
        $blogData = [
            'user_id' => $this->user->id,
            'title' => 'Test Blog Post',
            'slug' => 'test-blog-post',
            'subtitle' => 'A test subtitle',
            'content' => 'This is test content for the blog post.',
            'meta_title' => 'Custom SEO Title',
            'meta_description' => 'This is a custom SEO description for search engines.',
            'published' => true,
        ];

        $blog = Blog::create($blogData);

        $this->assertDatabaseHas('blogs', [
            'title' => 'Test Blog Post',
            'meta_title' => 'Custom SEO Title',
            'meta_description' => 'This is a custom SEO description for search engines.',
        ]);

        $this->assertEquals('Custom SEO Title', $blog->meta_title);
        $this->assertEquals('This is a custom SEO description for search engines.', $blog->meta_description);
    }

    public function test_blog_can_be_created_without_seo_fields(): void
    {
        $blogData = [
            'user_id' => $this->user->id,
            'title' => 'Test Blog Post Without SEO',
            'slug' => 'test-blog-post-without-seo',
            'content' => 'This is test content for the blog post.',
            'published' => true,
        ];

        $blog = Blog::create($blogData);

        $this->assertDatabaseHas('blogs', [
            'title' => 'Test Blog Post Without SEO',
            'meta_title' => null,
            'meta_description' => null,
        ]);

        $this->assertNull($blog->meta_title);
        $this->assertNull($blog->meta_description);
    }

    public function test_blog_factory_creates_seo_fields(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
        ]);

        // The factory has a 60% chance of creating SEO fields, so we'll test both cases
        $this->assertTrue(
            is_null($blog->meta_title) || is_string($blog->meta_title),
            'Meta title should be either null or a string'
        );

        $this->assertTrue(
            is_null($blog->meta_description) || is_string($blog->meta_description),
            'Meta description should be either null or a string'
        );
    }

    public function test_blog_show_page_uses_custom_seo_meta_title(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
            'title' => 'Original Blog Title',
            'meta_title' => 'Custom SEO Title',
            'published' => true,
        ]);

        $response = $this->get(route('blog.show', $blog->slug));

        $response->assertStatus(200);
        
        // Check that the custom meta title is used in the response
        $response->assertInertia(fn ($page) => 
            $page->where('meta.title', 'Custom SEO Title')
        );
    }

    public function test_blog_show_page_falls_back_to_default_title_when_no_custom_seo(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
            'title' => 'Original Blog Title',
            'meta_title' => null,
            'published' => true,
        ]);

        $response = $this->get(route('blog.show', $blog->slug));

        $response->assertStatus(200);
        
        // Check that the default title format is used
        $response->assertInertia(fn ($page) => 
            $page->where('meta.title', 'Original Blog Title - BookingBear Blog')
        );
    }

    public function test_blog_show_page_uses_custom_seo_meta_description(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
            'subtitle' => 'Original subtitle',
            'meta_description' => 'Custom SEO description for search engines',
            'published' => true,
        ]);

        $response = $this->get(route('blog.show', $blog->slug));

        $response->assertStatus(200);
        
        // Check that the custom meta description is used
        $response->assertInertia(fn ($page) => 
            $page->where('meta.description', 'Custom SEO description for search engines')
        );
    }

    public function test_blog_show_page_falls_back_to_subtitle_when_no_custom_meta_description(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
            'subtitle' => 'Original subtitle',
            'meta_description' => null,
            'published' => true,
        ]);

        $response = $this->get(route('blog.show', $blog->slug));

        $response->assertStatus(200);
        
        // Check that the subtitle is used as fallback
        $response->assertInertia(fn ($page) => 
            $page->where('meta.description', 'Original subtitle')
        );
    }

    public function test_seo_fields_are_mass_assignable(): void
    {
        $blogData = [
            'user_id' => $this->user->id,
            'title' => 'Test Mass Assignment',
            'content' => 'Test content',
            'meta_title' => 'Mass Assigned Meta Title',
            'meta_description' => 'Mass assigned meta description',
        ];

        $blog = new Blog();
        $blog->fill($blogData);
        $blog->save();

        $this->assertEquals('Mass Assigned Meta Title', $blog->meta_title);
        $this->assertEquals('Mass assigned meta description', $blog->meta_description);
    }

    public function test_seo_fields_can_be_updated(): void
    {
        $blog = Blog::factory()->create([
            'user_id' => $this->user->id,
            'meta_title' => 'Original Meta Title',
            'meta_description' => 'Original meta description',
        ]);

        $blog->update([
            'meta_title' => 'Updated Meta Title',
            'meta_description' => 'Updated meta description',
        ]);

        $this->assertEquals('Updated Meta Title', $blog->fresh()->meta_title);
        $this->assertEquals('Updated meta description', $blog->fresh()->meta_description);
    }
}
