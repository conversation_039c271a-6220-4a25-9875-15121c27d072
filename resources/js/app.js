import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy';
import { router } from '@inertiajs/vue3'

const appName = import.meta.env.VITE_APP_NAME || 'Laravel';

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue);

        // Load Preline after mount
        app.mount(el);
    },
    progress: {
        color: '#4B5563',
    },
});

/**
 * Track Page and Send to Google Analytic
 * */
if (process.env.NODE_ENV === "production") {
    router.on('navigate', (event) => {
        gtag("js", new Date());
        gtag("config", "G-CZLB459ZT3");
    });
}

import("preline/dist/index.js");
